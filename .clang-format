---
BasedOnStyle: Google
---
Language:                               Cpp
Cpp11BracedListStyle:                   true
Standard:                               Cpp11
CommentPragmas:                         '^ NOLINT'

AccessModifierOffset: -4
AlignAfterOpenBracket: AlwaysBreak
AlignOperands: false
AllowAllParametersOfDeclarationOnNextLine: false
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AlwaysBreakAfterReturnType: None
AlwaysBreakTemplateDeclarations: true
BinPackArguments: false
BinPackParameters: false
BreakConstructorInitializers: AfterColon
ColumnLimit: 120
ConstructorInitializerIndentWidth: 8
ContinuationIndentWidth: 8
DerivePointerAlignment: true
FixNamespaceComments: true
IndentCaseLabels: false
IndentWidth: 4
MaxEmptyLinesToKeep: 1
NamespaceIndentation: None
PenaltyBreakAssignment: 2
PenaltyBreakBeforeFirstCallParameter: 1
PenaltyBreakComment: 500
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 400
PointerAlignment: Left
SortIncludes: false
BreakBeforeBinaryOperators: true

# Mimic cpplint style
IncludeCategories:
  # Note that the "main" header is priority 0
  # The priority is assigned to first match in the ordered list
  # Miscelaneous system libraries
  - Regex:       '<(immintrin.h|malloc.h|wait.h|x86intrin.h|cuda.*)>'
    Priority:    3
  # C standard libraries
  - Regex:       '<(arpa/|netinet/|net/if|sys/)?[^\./]*\.h>'
    Priority:    1
  # C++ standard libraries
  - Regex:       '<[^/\./]*>'
    Priority:    2
  # Experimental or other system libraries
  - Regex:       '<'
    Priority:    3
  # Test libs
  - Regex:       '"(gtest|gmock)/'
    Priority:    4
  # Protobuf Files
  - Regex:       '\.pb\.h'
    Priority:    6
  # Apollo libs
  - Regex:       '^"(cyber|modules)'
    Priority:    7
  # The rest
  - Regex:       '.*'
    Priority:    5
---
