{"files.associations": {"*.inc": "cpp", "*.ipp": "cpp", "atomic": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cmath": "cpp", "complex": "cpp", "random": "cpp", "functional": "cpp", "future": "cpp", "limits": "cpp", "regex": "cpp", "tuple": "cpp", "type_traits": "cpp", "valarray": "cpp", "chrono": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "optional": "cpp", "string_view": "cpp", "algorithm": "cpp", "hash_map": "cpp", "fstream": "cpp", "iomanip": "cpp", "istream": "cpp", "mutex": "cpp", "ostream": "cpp", "numeric": "cpp", "ratio": "cpp", "sstream": "cpp", "streambuf": "cpp", "core": "cpp", "fft": "cpp", "*.txx": "cpp", "iostream": "cpp", "memory": "cpp", "*.cc": "cpp", "*.cu": "cpp", "array": "cpp", "hash_set": "cpp", "rope": "cpp", "slist": "cpp", "initializer_list": "cpp", "bit": "cpp", "cctype": "cpp", "cinttypes": "cpp", "clocale": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "map": "cpp", "set": "cpp", "exception": "cpp", "iterator": "cpp", "memory_resource": "cpp", "system_error": "cpp", "utility": "cpp", "iosfwd": "cpp", "new": "cpp", "shared_mutex": "cpp", "stdexcept": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "variant": "cpp"}, "git.ignoreLimitWarning": true, "search.useIgnoreFiles": false, "editor.formatOnSave": true}