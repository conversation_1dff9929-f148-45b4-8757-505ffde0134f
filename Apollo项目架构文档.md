# Apollo自动驾驶项目架构文档

## 项目概述

Apollo是百度开源的自动驾驶平台，本项目是基于Apollo Core Workspace的包管理开发调试样例工程。项目采用模块化设计，通过Cyber RT框架实现各模块间的通信和协调。

## 整体架构

### 系统层次结构

```
Apollo自动驾驶系统
├── 感知层 (Perception)
│   ├── 传感器数据处理
│   ├── 目标检测与识别
│   └── 交通灯识别
├── 预测层 (Prediction)
│   ├── 障碍物轨迹预测
│   └── 行为预测
├── 规划层 (Planning)
│   ├── 路径规划
│   ├── 速度规划
│   └── 轨迹优化
├── 控制层 (Control)
│   ├── 横向控制
│   └── 纵向控制
├── 定位层 (Localization)
│   ├── GPS/IMU融合
│   └── 高精地图匹配
├── 路由层 (Routing)
│   ├── 全局路径规划
│   └── 导航信息生成
└── 底盘层 (Canbus)
    ├── 车辆状态监控
    └── 执行器控制
```

### 数据流向

```
传感器数据 → 感知模块 → 预测模块 → 规划模块 → 控制模块 → 底盘执行
                ↓           ↓           ↓
            交通灯检测   障碍物预测   轨迹规划
                ↓           ↓           ↓
            定位模块 ← 高精地图 ← 路由模块
```

## 核心模块详解

### 1. Planning模块 (路径规划)

**位置**: `modules/planning/`

**功能**: 负责车辆的轨迹规划，是自动驾驶的核心决策模块

#### 子模块结构:
- **planning_component**: 规划组件的主入口和配置
- **planning_interface_base**: 规划接口基类定义
- **planning_base**: 规划模块的基础数据结构和算法库 ⭐ **新增**
- **scenarios**: 场景管理（车道保持、变道、停车等）
- **tasks**: 具体规划任务（路径生成、速度优化等）
- **traffic_rules**: 交通规则处理

#### 关键文件:
- `modules/planning/planning_component/planning_component.cc` - 主入口组件
- `modules/planning/planning_component/dag/planning.dag` - 模块配置
- `modules/planning/planning_base/` - 基础算法库和数据结构 ⭐ **新增**
- `modules/planning/scenarios/` - 各种驾驶场景实现
- `modules/planning/tasks/` - 规划任务实现

#### 输入数据:
- `/apollo/prediction` - 预测障碍物信息
- `/apollo/canbus/chassis` - 车辆底盘状态
- `/apollo/localization/pose` - 定位信息
- `/apollo/perception/traffic_light` - 交通灯信息

#### 输出数据:
- `/apollo/planning` - 规划轨迹
- `/apollo/planning/command_status` - 命令执行状态

### 2. Routing模块 (路由规划)

**位置**: `modules/routing/`

**功能**: 基于高精地图生成全局导航路径

#### 关键文件:
- `modules/routing/routing_component.cc` - 路由组件
- `modules/routing/core/navigator.cc` - 导航核心算法
- `modules/routing/dag/routing.dag` - 模块配置

#### 输入数据:
- `/apollo/raw_routing_request` - 路由请求（起点终点）
- 高精地图数据

#### 输出数据:
- 路由导航信息（传递给Planning模块）

### 3. Control模块 (控制)

**位置**: 通过core依赖包提供

**功能**: 将规划轨迹转换为具体的车辆控制指令

#### 输入数据:
- Planning模块输出的轨迹
- 车辆当前状态

#### 输出数据:
- 油门、刹车、转向控制指令

### 4. Canbus模块 (底盘通信)

**位置**: 通过core依赖包提供

**功能**: 与车辆底盘系统通信，获取车辆状态并执行控制指令

#### 输入数据:
- Control模块的控制指令

#### 输出数据:
- `/apollo/canbus/chassis` - 车辆底盘状态信息

### 5. Localization模块 (定位)

**位置**: 通过core依赖包提供

**功能**: 提供高精度车辆定位信息

#### 输出数据:
- `/apollo/localization/pose` - 车辆位置和姿态信息

### 6. Prediction模块 (预测)

**位置**: 通过core依赖包提供

**功能**: 预测周围障碍物的未来轨迹

#### 输出数据:
- `/apollo/prediction` - 障碍物预测信息

## 配置文件系统

### 目录结构
```
├── core/                    # 核心依赖包配置
├── profiles/               # 车辆配置文件
│   ├── default/           # 默认配置
│   ├── sample/            # 示例配置
│   └── current/           # 当前使用配置
├── data/                  # 数据文件
│   ├── map_data/         # 地图数据
│   ├── log/              # 日志文件
│   └── calibration_data/ # 标定数据
└── modules/              # 模块源码
```

### 重要配置文件
- `.workspace.json` - 软件包版本配置
- `WORKSPACE` - Bazel编译配置
- `profiles/*/modules/*/conf/` - 各模块配置文件
- `profiles/*/modules/*/dag/` - 模块DAG配置

## 故障排查指南

### 规划模块问题

#### 1. 轨迹规划失败
**现象**: 车辆无法生成合理轨迹
**排查文件**:
- `modules/planning/planning_component/planning_component.cc`
- `modules/planning/scenarios/` 下对应场景文件
- `modules/planning/tasks/` 下相关任务文件

#### 2. 场景切换异常
**现象**: 车辆在不同驾驶场景间切换异常
**排查文件**:
- `modules/planning/scenarios/` 下场景实现
- `modules/planning/planning_interface_base/scenario_base/`

#### 3. 交通规则处理错误
**现象**: 车辆不遵守交通规则
**排查文件**:
- `modules/planning/traffic_rules/` 下对应规则文件
- `modules/planning/traffic_rules/traffic_light/`
- `modules/planning/traffic_rules/stop_sign/`

### 路由模块问题

#### 1. 路径规划失败
**现象**: 无法生成从起点到终点的路径
**排查文件**:
- `modules/routing/core/navigator.cc`
- `modules/routing/strategy/a_star_strategy.cc`
- 检查地图数据完整性

#### 2. 路由响应超时
**现象**: 路由请求长时间无响应
**排查文件**:
- `modules/routing/routing_component.cc`
- `modules/routing/conf/routing_config.pb.txt`

### 通信问题

#### 1. 模块间通信异常
**现象**: 模块无法接收到其他模块数据
**排查文件**:
- 各模块的 `dag/*.dag` 配置文件
- 检查channel名称是否匹配
- 检查QoS配置

#### 2. 数据延迟过高
**现象**: 数据传输延迟导致系统响应慢
**排查文件**:
- DAG配置中的 `qos_profile` 设置
- `pending_queue_size` 参数调整

### 配置问题

#### 1. 模块启动失败
**现象**: 某个模块无法正常启动
**排查文件**:
- `profiles/current/modules/*/conf/` 配置文件
- `core/cyberfile.xml` 依赖配置
- 检查依赖包是否正确安装

#### 2. 参数配置错误
**现象**: 模块行为异常，参数不合理
**排查文件**:
- `modules/*/conf/*.pb.txt` 参数配置文件
- `modules/*/conf/*.conf` 标志文件

## 开发调试指南

### 编译系统
- 使用Bazel构建系统
- 主要编译文件: `BUILD` 文件
- 编译命令: `buildtool build`

### 日志系统
- 日志位置: `data/log/`
- 使用AINFO, AWARN, AERROR等宏记录日志
- 可通过Dreamview+查看实时日志

### 测试系统
- 单元测试文件通常以 `*_test.cc` 命名
- 集成测试位于 `integration_tests/` 目录

### 插件开发
1. **Scenario插件**: 继承 `apollo::planning::Scenario`
2. **Task插件**: 继承 `apollo::planning::Task`
3. **TrafficRule插件**: 继承 `apollo::planning::TrafficRule`

## 性能优化建议

### 1. 规划模块优化
- 调整规划频率参数
- 优化路径搜索算法参数
- 合理配置场景切换条件

### 2. 通信优化
- 调整QoS参数
- 优化消息队列大小
- 减少不必要的数据传输

### 3. 系统资源优化
- 监控CPU和内存使用
- 调整线程优先级
- 优化数据结构

## 常用命令

### 系统管理
```bash
# 启动容器
aem start

# 进入容器
aem enter

# 安装软件包
buildtool build

# 启动Dreamview+
aem bootstrap restart --plus

# 停止所有进程
bash kill_all.sh
```

### 调试命令
```bash
# 查看模块状态
cyber_monitor

# 查看话题信息
cyber_channel list

# 录制数据
cyber_recorder record

# 回放数据
cyber_recorder play
```

## Planning模块详细架构

### Planning模块工作流程

```
PlanningComponent::Proc()
    ↓
检查输入数据有效性
    ↓
选择规划模式 (OnLanePlanning/NaviPlanning)
    ↓
场景管理器选择当前场景
    ↓
执行交通规则检查
    ↓
场景内Stage执行
    ↓
Task任务执行 (路径生成、速度优化)
    ↓
轨迹优化与输出 (使用planning_base算法库)
```

### Planning_Base模块详解 ⭐ **新增模块**

**位置**: `modules/planning/planning_base/`

**功能**: Planning模块的基础数据结构和算法库，提供规划过程中的公共数据结构类和底层算法函数

#### 子模块结构:

1. **common** - 公共算法库
   - `frame.cc/h` - 规划帧数据管理
   - `reference_line_info.cc/h` - 参考线信息处理
   - `obstacle.cc/h` - 障碍物数据结构
   - `path/` - 路径相关数据结构
   - `speed/` - 速度相关数据结构
   - `trajectory/` - 轨迹相关数据结构

2. **math** - 基础数学库
   - `curve1d/` - 一维曲线数学库
   - `smoothing_spline/` - 样条曲线平滑算法
   - `piecewise_jerk/` - 分段加加速度算法
   - `discretized_points_smoothing/` - 离散点平滑算法
   - `constraint_checker/` - 约束检查器

3. **reference_line** - 参考线处理
   - `reference_line_provider.cc/h` - 参考线提供者
   - `discrete_points_reference_line_smoother.cc/h` - 离散点参考线平滑
   - `qp_spline_reference_line_smoother.cc/h` - QP样条参考线平滑
   - `spiral_reference_line_smoother.cc/h` - 螺旋线参考线平滑

4. **learning_based** - 基于学习的算法 ⭐ **AI/ML功能**
   - `model_inference/` - 模型推理
   - `img_feature_renderer/` - 图像特征渲染
   - `tuning/` - 自动调参
   - `pipeline/` - 学习数据管道

5. **proto** - 协议定义
   - `planning_config.proto` - 规划配置
   - `learning_data.proto` - 学习数据定义
   - `planning_status.proto` - 规划状态

6. **gflags** - 参数配置
   - `planning_gflags.cc/h` - 全局标志参数

7. **tools** - 工具类
   - `planning_task_stats.py` - 规划任务统计
   - `plot_*.py` - 可视化工具
   - `inference_demo.cc` - 推理演示

### Planning支持的场景类型

1. **LaneFollowScenario** - 车道保持场景
   - 文件位置: `modules/planning/scenarios/lane_follow/`
   - 功能: 沿道路行驶，避障，换道

2. **TrafficLightProtectedScenario** - 保护性交通灯场景
   - 文件位置: `modules/planning/scenarios/traffic_light_protected/`
   - 功能: 处理有保护的交通灯路口

3. **TrafficLightUnprotectedRightTurnScenario** - 非保护右转场景
   - 文件位置: `modules/planning/scenarios/traffic_light_unprotected_right_turn/`
   - 功能: 处理无保护右转情况

### Planning支持的任务类型

1. **LaneBorrowPath** - 借道路径规划
   - 文件位置: `modules/planning/tasks/lane_borrow_path/`
   - 功能: 生成借道超车路径

### 交通规则模块

1. **Crosswalk** - 人行道规则
   - 文件位置: `modules/planning/traffic_rules/crosswalk/`
   - 功能: 处理人行道停车让行

2. **StopSign** - 停车标志规则
   - 文件位置: `modules/planning/traffic_rules/stop_sign/`
   - 功能: 处理停车标志

3. **TrafficLight** - 交通灯规则
   - 文件位置: `modules/planning/traffic_rules/traffic_light/`
   - 功能: 处理交通信号灯

## 路径优化相关文件

### 路径优化工具
- **位置**: `modules/planning/planning_interface_base/task_base/common/path_util/path_optimizer_util.cc`
- **功能**: 提供路径优化的通用工具函数
- **用途**: 路径平滑、曲率优化、碰撞检测等

### 车道变更工具
- **位置**: `modules/planning/planning_interface_base/task_base/common/lane_change_util/`
- **功能**: 提供车道变更相关的工具函数
- **用途**: 车道变更决策、安全性检查等

### Planning_Base数学库 ⭐ **新增**
- **位置**: `modules/planning/planning_base/math/`
- **功能**: 提供规划算法的核心数学库
- **主要算法**:
  - **样条曲线平滑**: `smoothing_spline/` - 用于轨迹平滑
  - **分段加加速度**: `piecewise_jerk/` - 用于舒适性优化
  - **离散点平滑**: `discretized_points_smoothing/` - 用于路径点平滑
  - **曲线数学**: `curve1d/` - 一维曲线计算
  - **约束检查**: `constraint_checker/` - 动力学约束验证

### 参考线处理模块 ⭐ **新增**
- **位置**: `modules/planning/planning_base/reference_line/`
- **功能**: 参考线生成和平滑处理
- **关键算法**:
  - **离散点平滑**: `discrete_points_reference_line_smoother.cc`
  - **QP样条平滑**: `qp_spline_reference_line_smoother.cc`
  - **螺旋线平滑**: `spiral_reference_line_smoother.cc`

## 模块间通信机制

### Cyber RT通信框架

Apollo使用Cyber RT作为通信中间件，支持：
- **发布/订阅模式**: 异步消息传递
- **服务/客户端模式**: 同步请求响应
- **参数服务**: 配置参数管理

### 关键通信Channel

| Channel名称 | 数据类型 | 发布者 | 订阅者 | 功能描述 |
|------------|----------|--------|--------|----------|
| `/apollo/prediction` | PredictionObstacles | Prediction | Planning | 障碍物预测信息 |
| `/apollo/canbus/chassis` | Chassis | Canbus | Planning/Control | 车辆底盘状态 |
| `/apollo/localization/pose` | LocalizationEstimate | Localization | Planning | 车辆定位信息 |
| `/apollo/planning` | ADCTrajectory | Planning | Control | 规划轨迹 |
| `/apollo/raw_routing_request` | RoutingRequest | External | Routing | 路由请求 |

## 故障排查详细指南

### Planning模块故障排查

#### 路径优化问题
**现象**: 生成的路径不平滑或存在急转弯
**排查步骤**:
1. 检查 `path_optimizer_util.cc` 中的优化参数
2. 查看路径生成任务的配置文件
3. 检查地图数据质量
4. 验证车辆动力学约束参数
5. ⭐ **新增**: 检查planning_base数学库中的平滑算法参数

**关键文件**:
- `modules/planning/planning_interface_base/task_base/common/path_util/path_optimizer_util.cc`
- `modules/planning/tasks/*/conf/*.pb.txt`
- ⭐ **新增**: `modules/planning/planning_base/math/smoothing_spline/` - 样条平滑算法
- ⭐ **新增**: `modules/planning/planning_base/math/discretized_points_smoothing/` - 离散点平滑
- ⭐ **新增**: `modules/planning/planning_base/math/piecewise_jerk/` - 分段加加速度优化

#### 车道变更失败
**现象**: 车辆无法正常变道或变道决策错误
**排查步骤**:
1. 检查车道变更条件判断逻辑
2. 验证周围车辆检测是否正确
3. 检查安全距离参数设置
4. 查看车道变更任务执行日志

**关键文件**:
- `modules/planning/planning_interface_base/task_base/common/lane_change_util/`
- `modules/planning/tasks/lane_borrow_path/`
- ⭐ **新增**: `modules/planning/planning_base/common/obstacle.cc/h` - 障碍物处理
- ⭐ **新增**: `modules/planning/planning_base/common/path_decision.cc/h` - 路径决策

#### 场景切换异常
**现象**: 车辆在不同场景间切换不及时或错误
**排查步骤**:
1. 检查场景切换条件
2. 验证传感器数据是否正确
3. 查看场景管理器日志
4. 检查场景优先级设置

**关键文件**:
- `modules/planning/scenarios/*/`
- `modules/planning/planning_interface_base/scenario_base/`
- ⭐ **新增**: `modules/planning/planning_base/common/frame.cc/h` - 规划帧管理
- ⭐ **新增**: `modules/planning/planning_base/common/reference_line_info.cc/h` - 参考线信息

### 系统性能问题

#### 规划延迟过高
**现象**: 规划模块响应时间过长
**排查步骤**:
1. 检查CPU使用率
2. 分析规划算法复杂度
3. 优化路径搜索范围
4. 调整规划频率

**优化建议**:
- 减少搜索空间
- 使用更高效的算法
- 并行化计算
- 缓存重复计算结果

#### 内存使用过高
**现象**: 系统内存占用持续增长
**排查步骤**:
1. 检查内存泄漏
2. 优化数据结构
3. 及时释放不用的对象
4. 使用内存池技术

## 开发最佳实践

### 代码规范
- 遵循Google C++代码规范
- 使用CPPLINT进行代码检查
- 添加详细的注释和文档

### 测试策略
- 编写单元测试覆盖核心逻辑
- 进行集成测试验证模块协作
- 使用仿真环境进行功能测试
- 在真实环境中进行验证

### 调试技巧
- 使用Dreamview+进行可视化调试
- 利用cyber_monitor监控消息流
- 记录详细的调试日志
- 使用数据回放进行问题复现

## 扩展开发指南

### 添加新的规划场景
1. 继承 `apollo::planning::Scenario` 基类
2. 实现场景特定的逻辑
3. 定义场景切换条件
4. 添加配置文件和BUILD规则
5. 注册场景到场景管理器

### 添加新的规划任务
1. 继承相应的Task基类
2. 实现 `Init()` 和 `Execute()` 方法
3. 添加任务配置文件
4. 在场景中调用新任务
5. 编写单元测试

### 添加新的交通规则
1. 继承 `apollo::planning::TrafficRule` 基类
2. 实现 `Init()` 和 `ApplyRule()` 方法
3. 添加规则配置文件
4. 注册到交通规则管理器
5. 测试规则有效性

## AI/ML功能模块 ⭐ **新增功能**

### 基于学习的规划算法

**位置**: `modules/planning/planning_base/learning_based/`

Apollo现在集成了基于机器学习的规划算法，提供更智能的决策能力。

#### 主要功能模块:

1. **模型推理** (`model_inference/`)
   - `trajectory_imitation_libtorch_inference.cc/h` - 轨迹模仿学习推理
   - 支持PyTorch模型部署
   - 实时轨迹预测和生成

2. **图像特征渲染** (`img_feature_renderer/`)
   - `birdview_img_feature_renderer.cc/h` - 鸟瞰图特征渲染
   - 将环境信息转换为图像特征
   - 为深度学习模型提供输入

3. **自动调参** (`tuning/`)
   - `autotuning_mlp_net_model.cc/h` - MLP网络自动调参
   - `autotuning_raw_feature_generator.cc/h` - 原始特征生成
   - 基于强化学习的参数优化

4. **数据管道** (`pipeline/`)
   - `record_to_learning_data.cc` - 记录数据转换为学习数据
   - `evaluate_trajectory.cc` - 轨迹评估
   - 数据预处理和后处理

#### 使用场景:
- **轨迹模仿学习**: 学习人类驾驶员的驾驶行为
- **环境感知增强**: 通过图像特征提升环境理解
- **参数自动优化**: 根据驾驶场景自动调整规划参数
- **性能评估**: 评估规划算法的性能表现

#### 故障排查:
**现象**: AI模型推理失败或性能下降
**排查文件**:
- `modules/planning/planning_base/learning_based/model_inference/`
- `modules/planning/planning_base/learning_based/data/model/` - 模型文件
- `modules/planning/planning_base/tools/inference_demo.cc` - 推理测试工具

### 可视化和调试工具 ⭐ **新增工具**

**位置**: `modules/planning/planning_base/tools/`

#### 主要工具:
1. **性能统计**: `planning_task_stats.py` - 分析规划任务性能
2. **可视化工具**:
   - `plot_planning_perf.py` - 规划性能可视化
   - `plot_log.py` - 日志数据可视化
   - `plot_st.json` - ST图可视化配置
   - `plot_sl.json` - SL图可视化配置
3. **推理演示**: `inference_demo.cc` - AI模型推理演示

#### 使用方法:
```bash
# 统计规划任务性能
python3 modules/planning/planning_base/tools/planning_task_stats.py

# 可视化规划性能
python3 modules/planning/planning_base/tools/plot_planning_perf.py

# 运行推理演示
./bazel-bin/modules/planning/planning_base/inference_demo
```

---

**注意**: 本文档基于当前项目结构生成，如有模块更新或配置变更，请及时更新此文档。
