--flagfile=modules/common/data/global_flagfile.txt
--traffic_rule_config_filename=modules/planning/planning_component/conf/traffic_rule_config.pb.txt
--planning_upper_speed_limit=16.6
--default_cruise_speed=16.6
--ignore_overlapped_obstacle=true
--prioritize_change_lane
--min_length_for_lane_change=5.0
--nouse_multi_thread_to_add_obstacles
# --min_past_history_points_len=10
--enable_print_curve=false
--destination_check_distance=4.0
# --smoother_config_filename=modules/planning/planning_component/conf/spiral_smoother_config.pb.txt
# --smoother_config_filename=modules/planning/planning_component/conf/qp_spline_smoother_config.pb.txt
--smoother_config_filename=modules/planning/planning_component/conf/discrete_points_smoother_config.pb.txt
--enable_reference_line_stitching=false
# --speed_bump_speed_limit=3
# --parking_inwards=false
# --use_dual_variable_warm_start=true
# --enable_record_debug=true
# --enable_parallel_hybrid_a=true
--export_chart=true

# --use_front_axe_center_in_path_planning=true

--enable_smoother_failsafe
--enable_parallel_trajectory_smoothing
--nouse_s_curve_speed_smooth
--use_iterative_anchoring_smoother

--nonstatic_obstacle_nudge_l_buffer=0.4

--use_st_drivable_boundary=false

--enable_pull_over_at_destination=false

--enable_smart_obstacle_filtering_in_borrow=true
--lane_borrow_side_obstacle_ignore_distance=1

# 障碍物横向距离
--obstacle_lat_buffer=0.5

--speed_bump_speed_limit=3.4
--static_obstacle_speed_threshold=3
--longitudinal_acceleration_lower_bound = -5.7
--longitudinal_acceleration_upper_bound = 2.9
--longitudinal_jerk_lower_bound =-6
--longitudinal_jerk_upper_bound =6


# 开放空间
#  false  true false false
# --enable_smoother_failsafe=false
# --use_dual_variable_warm_start=true
# --use_iterative_anchoring_smoother=false
# --enable_parallel_trajectory_smoothing=false
