--flagfile=/apollo/modules/common/data/global_flagfile.txt
--static_file_dir=/apollo/modules/dreamview_plus/frontend/dist
--default_data_collection_config_path=/apollo/modules/dreamview_plus/conf/data_collection_table.pb.txt
--default_preprocess_config_path=/apollo/modules/dreamview_plus/conf/preprocess_table.pb.txt
--data_handler_config_path=/apollo/modules/dreamview_plus/conf/data_handler.conf
--vehicle_data_config_filename=/apollo/modules/dreamview_plus/conf/vehicle_data.pb.txt
--default_hmi_mode=Default
--server_ports=8888
# If you are in package develop mode, use profiles change vehicle type.
--vehicles_config_path=/apollo_workspace/profiles
--vehicle_changed_use_copy_mode=false
# When the monitor message delay exceeds this time,
# the system will automatically exit the automatic driving mode.
# The default time is one day.
--monitor_timeout_threshold=86400
