mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/monitor/dag/monitor.dag
-p
monitor
-s
CYBER_DEFAULT
"
mainboard_process_context_switches_involuntary_second : 4
mainboard_process_context_switches_voluntary_second : 11473
mainboard_process_cpu_usage : 0.038
mainboard_process_cpu_usage_system : 0.030
mainboard_process_cpu_usage_user : 0.008
mainboard_process_disk_read_bytes_second : 0
mainboard_process_disk_write_bytes_second : 2048
mainboard_process_faults_major : 157
mainboard_process_faults_minor_second : 0
mainboard_process_fd_count : 111
mainboard_process_inblocks_second : 0
mainboard_process_io_read_bytes_second : 20818
mainboard_process_io_read_second : 334
mainboard_process_io_write_bytes_second : 857
mainboard_process_io_write_second : 1
mainboard_process_memory_data_and_stack : 553525248
mainboard_process_memory_resident : 94904320
mainboard_process_memory_shared : 57696256
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 867319808
mainboard_process_nice : 0
mainboard_process_outblocks_second : 4
mainboard_process_priority : 20
mainboard_process_thread_count : 35
mainboard_process_uptime : 8211.779075
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
