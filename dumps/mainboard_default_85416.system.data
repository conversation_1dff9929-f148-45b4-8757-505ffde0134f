mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/studio_connector/studio_connector.dag
-p
mainboard_default_85416
-s
CYBER_DEFAULT
--disable_plugin_autoload
"
mainboard_process_context_switches_involuntary_second : 8
mainboard_process_context_switches_voluntary_second : 10772
mainboard_process_cpu_usage : 0.042
mainboard_process_cpu_usage_system : 0.023
mainboard_process_cpu_usage_user : 0.019
mainboard_process_disk_read_bytes_second : 195369
mainboard_process_disk_write_bytes_second : 1638
mainboard_process_faults_major : 154
mainboard_process_faults_minor_second : 16
mainboard_process_fd_count : 114
mainboard_process_inblocks_second : 382
mainboard_process_io_read_bytes_second : 24121
mainboard_process_io_read_second : 15
mainboard_process_io_write_bytes_second : 862
mainboard_process_io_write_second : 3
mainboard_process_memory_data_and_stack : 576471040
mainboard_process_memory_resident : 264101888
mainboard_process_memory_shared : 84250624
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 1017909248
mainboard_process_nice : 0
mainboard_process_outblocks_second : 3
mainboard_process_priority : 20
mainboard_process_thread_count : 38
mainboard_process_uptime : 5440.636739
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
