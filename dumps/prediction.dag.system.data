mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/prediction/dag/prediction.dag
"
mainboard_process_context_switches_involuntary_second : 4
mainboard_process_context_switches_voluntary_second : 10907
mainboard_process_cpu_usage : 0.033
mainboard_process_cpu_usage_system : 0.024
mainboard_process_cpu_usage_user : 0.009
mainboard_process_disk_read_bytes_second : 0
mainboard_process_disk_write_bytes_second : 1638
mainboard_process_faults_major : 325
mainboard_process_faults_minor_second : 0
mainboard_process_fd_count : 145
mainboard_process_inblocks_second : 0
mainboard_process_io_read_bytes_second : 591
mainboard_process_io_read_second : 5
mainboard_process_io_write_bytes_second : 965
mainboard_process_io_write_second : 0
mainboard_process_memory_data_and_stack : 1987072000
mainboard_process_memory_resident : 923750400
mainboard_process_memory_shared : 196620288
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 5075890176
mainboard_process_nice : 0
mainboard_process_outblocks_second : 3
mainboard_process_priority : 20
mainboard_process_thread_count : 62
mainboard_process_uptime : 8191.550212
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
