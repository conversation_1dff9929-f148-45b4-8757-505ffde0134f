mainboard_cpu_usage_latency : 86
mainboard_cpu_usage_latency_80 : 87
mainboard_cpu_usage_latency_90 : 87
mainboard_cpu_usage_latency_99 : 87
mainboard_cpu_usage_latency_999 : 87
mainboard_cpu_usage_latency_9999 : 87
mainboard_cpu_usage_max_latency : 88
mainboard_mem_resident_usage_latency : 758240
mainboard_mem_resident_usage_latency_80 : 758240
mainboard_mem_resident_usage_latency_90 : 758240
mainboard_mem_resident_usage_latency_99 : 758240
mainboard_mem_resident_usage_latency_999 : 758240
mainboard_mem_resident_usage_latency_9999 : 758240
mainboard_mem_resident_usage_max_latency : 758240
mainboard_message_reader_apollo_canbus_chassis_cyber_latency : 0
mainboard_message_reader_apollo_canbus_chassis_cyber_latency_80 : 0
mainboard_message_reader_apollo_canbus_chassis_cyber_latency_90 : 0
mainboard_message_reader_apollo_canbus_chassis_cyber_latency_99 : 0
mainboard_message_reader_apollo_canbus_chassis_cyber_latency_999 : 0
mainboard_message_reader_apollo_canbus_chassis_cyber_latency_9999 : 0
mainboard_message_reader_apollo_canbus_chassis_cyber_max_latency : 0
mainboard_message_reader_apollo_canbus_chassis_proc_latency : 0
mainboard_message_reader_apollo_canbus_chassis_proc_latency_80 : 0
mainboard_message_reader_apollo_canbus_chassis_proc_latency_90 : 0
mainboard_message_reader_apollo_canbus_chassis_proc_latency_99 : 0
mainboard_message_reader_apollo_canbus_chassis_proc_latency_999 : 0
mainboard_message_reader_apollo_canbus_chassis_proc_latency_9999 : 0
mainboard_message_reader_apollo_canbus_chassis_proc_max_latency : 0
mainboard_message_reader_apollo_canbus_chassis_tran_latency : 0
mainboard_message_reader_apollo_canbus_chassis_tran_latency_80 : 0
mainboard_message_reader_apollo_canbus_chassis_tran_latency_90 : 0
mainboard_message_reader_apollo_canbus_chassis_tran_latency_99 : 0
mainboard_message_reader_apollo_canbus_chassis_tran_latency_999 : 0
mainboard_message_reader_apollo_canbus_chassis_tran_latency_9999 : 0
mainboard_message_reader_apollo_canbus_chassis_tran_max_latency : 0
mainboard_message_reader_apollo_localization_pose_cyber_latency : 0
mainboard_message_reader_apollo_localization_pose_cyber_latency_80 : 0
mainboard_message_reader_apollo_localization_pose_cyber_latency_90 : 0
mainboard_message_reader_apollo_localization_pose_cyber_latency_99 : 0
mainboard_message_reader_apollo_localization_pose_cyber_latency_999 : 0
mainboard_message_reader_apollo_localization_pose_cyber_latency_9999 : 0
mainboard_message_reader_apollo_localization_pose_cyber_max_latency : 0
mainboard_message_reader_apollo_localization_pose_proc_latency : 0
mainboard_message_reader_apollo_localization_pose_proc_latency_80 : 0
mainboard_message_reader_apollo_localization_pose_proc_latency_90 : 0
mainboard_message_reader_apollo_localization_pose_proc_latency_99 : 0
mainboard_message_reader_apollo_localization_pose_proc_latency_999 : 0
mainboard_message_reader_apollo_localization_pose_proc_latency_9999 : 0
mainboard_message_reader_apollo_localization_pose_proc_max_latency : 0
mainboard_message_reader_apollo_localization_pose_tran_latency : 0
mainboard_message_reader_apollo_localization_pose_tran_latency_80 : 0
mainboard_message_reader_apollo_localization_pose_tran_latency_90 : 0
mainboard_message_reader_apollo_localization_pose_tran_latency_99 : 0
mainboard_message_reader_apollo_localization_pose_tran_latency_999 : 0
mainboard_message_reader_apollo_localization_pose_tran_latency_9999 : 0
mainboard_message_reader_apollo_localization_pose_tran_max_latency : 0
mainboard_message_reader_apollo_planning_command_status_cyber_latency : 0
mainboard_message_reader_apollo_planning_command_status_cyber_latency_80 : 0
mainboard_message_reader_apollo_planning_command_status_cyber_latency_90 : 0
mainboard_message_reader_apollo_planning_command_status_cyber_latency_99 : 0
mainboard_message_reader_apollo_planning_command_status_cyber_latency_999 : 0
mainboard_message_reader_apollo_planning_command_status_cyber_latency_9999 : 0
mainboard_message_reader_apollo_planning_command_status_cyber_max_latency : 0
mainboard_message_reader_apollo_planning_command_status_proc_latency : 0
mainboard_message_reader_apollo_planning_command_status_proc_latency_80 : 0
mainboard_message_reader_apollo_planning_command_status_proc_latency_90 : 0
mainboard_message_reader_apollo_planning_command_status_proc_latency_99 : 0
mainboard_message_reader_apollo_planning_command_status_proc_latency_999 : 0
mainboard_message_reader_apollo_planning_command_status_proc_latency_9999 : 0
mainboard_message_reader_apollo_planning_command_status_proc_max_latency : 0
mainboard_message_reader_apollo_planning_command_status_tran_latency : 0
mainboard_message_reader_apollo_planning_command_status_tran_latency_80 : 0
mainboard_message_reader_apollo_planning_command_status_tran_latency_90 : 0
mainboard_message_reader_apollo_planning_command_status_tran_latency_99 : 0
mainboard_message_reader_apollo_planning_command_status_tran_latency_999 : 0
mainboard_message_reader_apollo_planning_command_status_tran_latency_9999 : 0
mainboard_message_reader_apollo_planning_command_status_tran_max_latency : 0
mainboard_message_reader_apollo_planning_cyber_latency : 0
mainboard_message_reader_apollo_planning_cyber_latency_80 : 0
mainboard_message_reader_apollo_planning_cyber_latency_90 : 0
mainboard_message_reader_apollo_planning_cyber_latency_99 : 0
mainboard_message_reader_apollo_planning_cyber_latency_999 : 0
mainboard_message_reader_apollo_planning_cyber_latency_9999 : 0
mainboard_message_reader_apollo_planning_cyber_max_latency : 0
mainboard_message_reader_apollo_planning_proc_latency : 0
mainboard_message_reader_apollo_planning_proc_latency_80 : 0
mainboard_message_reader_apollo_planning_proc_latency_90 : 0
mainboard_message_reader_apollo_planning_proc_latency_99 : 0
mainboard_message_reader_apollo_planning_proc_latency_999 : 0
mainboard_message_reader_apollo_planning_proc_latency_9999 : 0
mainboard_message_reader_apollo_planning_proc_max_latency : 0
mainboard_message_reader_apollo_planning_tran_latency : 0
mainboard_message_reader_apollo_planning_tran_latency_80 : 0
mainboard_message_reader_apollo_planning_tran_latency_90 : 0
mainboard_message_reader_apollo_planning_tran_latency_99 : 0
mainboard_message_reader_apollo_planning_tran_latency_999 : 0
mainboard_message_reader_apollo_planning_tran_latency_9999 : 0
mainboard_message_reader_apollo_planning_tran_max_latency : 0
mainboard_planning_apollo_canbus_chassis_cyber_latency : 0
mainboard_planning_apollo_canbus_chassis_cyber_latency_80 : 0
mainboard_planning_apollo_canbus_chassis_cyber_latency_90 : 0
mainboard_planning_apollo_canbus_chassis_cyber_latency_99 : 0
mainboard_planning_apollo_canbus_chassis_cyber_latency_999 : 0
mainboard_planning_apollo_canbus_chassis_cyber_latency_9999 : 0
mainboard_planning_apollo_canbus_chassis_cyber_max_latency : 0
mainboard_planning_apollo_canbus_chassis_proc_latency : 0
mainboard_planning_apollo_canbus_chassis_proc_latency_80 : 0
mainboard_planning_apollo_canbus_chassis_proc_latency_90 : 0
mainboard_planning_apollo_canbus_chassis_proc_latency_99 : 0
mainboard_planning_apollo_canbus_chassis_proc_latency_999 : 0
mainboard_planning_apollo_canbus_chassis_proc_latency_9999 : 0
mainboard_planning_apollo_canbus_chassis_proc_max_latency : 0
mainboard_planning_apollo_canbus_chassis_tran_latency : 239
mainboard_planning_apollo_canbus_chassis_tran_latency_80 : 171
mainboard_planning_apollo_canbus_chassis_tran_latency_90 : 268
mainboard_planning_apollo_canbus_chassis_tran_latency_99 : 3963
mainboard_planning_apollo_canbus_chassis_tran_latency_999 : 13181
mainboard_planning_apollo_canbus_chassis_tran_latency_9999 : 13181
mainboard_planning_apollo_canbus_chassis_tran_max_latency : 13181
mainboard_planning_apollo_control_interactive_cyber_latency : 0
mainboard_planning_apollo_control_interactive_cyber_latency_80 : 0
mainboard_planning_apollo_control_interactive_cyber_latency_90 : 0
mainboard_planning_apollo_control_interactive_cyber_latency_99 : 0
mainboard_planning_apollo_control_interactive_cyber_latency_999 : 0
mainboard_planning_apollo_control_interactive_cyber_latency_9999 : 0
mainboard_planning_apollo_control_interactive_cyber_max_latency : 0
mainboard_planning_apollo_control_interactive_proc_latency : 0
mainboard_planning_apollo_control_interactive_proc_latency_80 : 0
mainboard_planning_apollo_control_interactive_proc_latency_90 : 0
mainboard_planning_apollo_control_interactive_proc_latency_99 : 0
mainboard_planning_apollo_control_interactive_proc_latency_999 : 0
mainboard_planning_apollo_control_interactive_proc_latency_9999 : 0
mainboard_planning_apollo_control_interactive_proc_max_latency : 0
mainboard_planning_apollo_control_interactive_tran_latency : 0
mainboard_planning_apollo_control_interactive_tran_latency_80 : 0
mainboard_planning_apollo_control_interactive_tran_latency_90 : 0
mainboard_planning_apollo_control_interactive_tran_latency_99 : 0
mainboard_planning_apollo_control_interactive_tran_latency_999 : 0
mainboard_planning_apollo_control_interactive_tran_latency_9999 : 0
mainboard_planning_apollo_control_interactive_tran_max_latency : 0
mainboard_planning_apollo_localization_pose_cyber_latency : 0
mainboard_planning_apollo_localization_pose_cyber_latency_80 : 0
mainboard_planning_apollo_localization_pose_cyber_latency_90 : 0
mainboard_planning_apollo_localization_pose_cyber_latency_99 : 0
mainboard_planning_apollo_localization_pose_cyber_latency_999 : 0
mainboard_planning_apollo_localization_pose_cyber_latency_9999 : 0
mainboard_planning_apollo_localization_pose_cyber_max_latency : 0
mainboard_planning_apollo_localization_pose_proc_latency : 0
mainboard_planning_apollo_localization_pose_proc_latency_80 : 0
mainboard_planning_apollo_localization_pose_proc_latency_90 : 0
mainboard_planning_apollo_localization_pose_proc_latency_99 : 0
mainboard_planning_apollo_localization_pose_proc_latency_999 : 0
mainboard_planning_apollo_localization_pose_proc_latency_9999 : 0
mainboard_planning_apollo_localization_pose_proc_max_latency : 0
mainboard_planning_apollo_localization_pose_tran_latency : 268
mainboard_planning_apollo_localization_pose_tran_latency_80 : 183
mainboard_planning_apollo_localization_pose_tran_latency_90 : 305
mainboard_planning_apollo_localization_pose_tran_latency_99 : 5004
mainboard_planning_apollo_localization_pose_tran_latency_999 : 13131
mainboard_planning_apollo_localization_pose_tran_latency_9999 : 13131
mainboard_planning_apollo_localization_pose_tran_max_latency : 13131
mainboard_planning_apollo_perception_traffic_light_cyber_latency : 140
mainboard_planning_apollo_perception_traffic_light_cyber_latency_80 : 180
mainboard_planning_apollo_perception_traffic_light_cyber_latency_90 : 224
mainboard_planning_apollo_perception_traffic_light_cyber_latency_99 : 3325
mainboard_planning_apollo_perception_traffic_light_cyber_latency_999 : 3325
mainboard_planning_apollo_perception_traffic_light_cyber_latency_9999 : 3325
mainboard_planning_apollo_perception_traffic_light_cyber_max_latency : 3325
mainboard_planning_apollo_perception_traffic_light_proc_latency : 11
mainboard_planning_apollo_perception_traffic_light_proc_latency_80 : 12
mainboard_planning_apollo_perception_traffic_light_proc_latency_90 : 19
mainboard_planning_apollo_perception_traffic_light_proc_latency_99 : 71
mainboard_planning_apollo_perception_traffic_light_proc_latency_999 : 71
mainboard_planning_apollo_perception_traffic_light_proc_latency_9999 : 71
mainboard_planning_apollo_perception_traffic_light_proc_max_latency : 71
mainboard_planning_apollo_perception_traffic_light_tran_latency : 270
mainboard_planning_apollo_perception_traffic_light_tran_latency_80 : 169
mainboard_planning_apollo_perception_traffic_light_tran_latency_90 : 274
mainboard_planning_apollo_perception_traffic_light_tran_latency_99 : 12626
mainboard_planning_apollo_perception_traffic_light_tran_latency_999 : 12626
mainboard_planning_apollo_perception_traffic_light_tran_latency_9999 : 12626
mainboard_planning_apollo_perception_traffic_light_tran_max_latency : 12626
mainboard_planning_apollo_planning_command_cyber_latency : 0
mainboard_planning_apollo_planning_command_cyber_latency_80 : 0
mainboard_planning_apollo_planning_command_cyber_latency_90 : 0
mainboard_planning_apollo_planning_command_cyber_latency_99 : 0
mainboard_planning_apollo_planning_command_cyber_latency_999 : 0
mainboard_planning_apollo_planning_command_cyber_latency_9999 : 0
mainboard_planning_apollo_planning_command_cyber_max_latency : 0
mainboard_planning_apollo_planning_command_proc_latency : 0
mainboard_planning_apollo_planning_command_proc_latency_80 : 0
mainboard_planning_apollo_planning_command_proc_latency_90 : 0
mainboard_planning_apollo_planning_command_proc_latency_99 : 0
mainboard_planning_apollo_planning_command_proc_latency_999 : 0
mainboard_planning_apollo_planning_command_proc_latency_9999 : 0
mainboard_planning_apollo_planning_command_proc_max_latency : 0
mainboard_planning_apollo_planning_command_tran_latency : 0
mainboard_planning_apollo_planning_command_tran_latency_80 : 0
mainboard_planning_apollo_planning_command_tran_latency_90 : 0
mainboard_planning_apollo_planning_command_tran_latency_99 : 0
mainboard_planning_apollo_planning_command_tran_latency_999 : 0
mainboard_planning_apollo_planning_command_tran_latency_9999 : 0
mainboard_planning_apollo_planning_command_tran_max_latency : 0
mainboard_planning_apollo_planning_pad_cyber_latency : 0
mainboard_planning_apollo_planning_pad_cyber_latency_80 : 0
mainboard_planning_apollo_planning_pad_cyber_latency_90 : 0
mainboard_planning_apollo_planning_pad_cyber_latency_99 : 0
mainboard_planning_apollo_planning_pad_cyber_latency_999 : 0
mainboard_planning_apollo_planning_pad_cyber_latency_9999 : 0
mainboard_planning_apollo_planning_pad_cyber_max_latency : 0
mainboard_planning_apollo_planning_pad_proc_latency : 0
mainboard_planning_apollo_planning_pad_proc_latency_80 : 0
mainboard_planning_apollo_planning_pad_proc_latency_90 : 0
mainboard_planning_apollo_planning_pad_proc_latency_99 : 0
mainboard_planning_apollo_planning_pad_proc_latency_999 : 0
mainboard_planning_apollo_planning_pad_proc_latency_9999 : 0
mainboard_planning_apollo_planning_pad_proc_max_latency : 0
mainboard_planning_apollo_planning_pad_tran_latency : 0
mainboard_planning_apollo_planning_pad_tran_latency_80 : 0
mainboard_planning_apollo_planning_pad_tran_latency_90 : 0
mainboard_planning_apollo_planning_pad_tran_latency_99 : 0
mainboard_planning_apollo_planning_pad_tran_latency_999 : 0
mainboard_planning_apollo_planning_pad_tran_latency_9999 : 0
mainboard_planning_apollo_planning_pad_tran_max_latency : 0
mainboard_planning_apollo_prediction_cyber_latency : 196
mainboard_planning_apollo_prediction_cyber_latency_80 : 79
mainboard_planning_apollo_prediction_cyber_latency_90 : 120
mainboard_planning_apollo_prediction_cyber_latency_99 : 6508
mainboard_planning_apollo_prediction_cyber_latency_999 : 6508
mainboard_planning_apollo_prediction_cyber_latency_9999 : 6508
mainboard_planning_apollo_prediction_cyber_max_latency : 6508
mainboard_planning_apollo_prediction_proc_latency : 8225
mainboard_planning_apollo_prediction_proc_latency_80 : 9087
mainboard_planning_apollo_prediction_proc_latency_90 : 11289
mainboard_planning_apollo_prediction_proc_latency_99 : 18402
mainboard_planning_apollo_prediction_proc_latency_999 : 18402
mainboard_planning_apollo_prediction_proc_latency_9999 : 18402
mainboard_planning_apollo_prediction_proc_max_latency : 18402
mainboard_planning_apollo_prediction_tran_latency : 505
mainboard_planning_apollo_prediction_tran_latency_80 : 372
mainboard_planning_apollo_prediction_tran_latency_90 : 1305
mainboard_planning_apollo_prediction_tran_latency_99 : 5036
mainboard_planning_apollo_prediction_tran_latency_999 : 5036
mainboard_planning_apollo_prediction_tran_latency_9999 : 5036
mainboard_planning_apollo_prediction_tran_max_latency : 5036
mainboard_planning_apollo_storytelling_cyber_latency : 0
mainboard_planning_apollo_storytelling_cyber_latency_80 : 0
mainboard_planning_apollo_storytelling_cyber_latency_90 : 0
mainboard_planning_apollo_storytelling_cyber_latency_99 : 0
mainboard_planning_apollo_storytelling_cyber_latency_999 : 0
mainboard_planning_apollo_storytelling_cyber_latency_9999 : 0
mainboard_planning_apollo_storytelling_cyber_max_latency : 0
mainboard_planning_apollo_storytelling_proc_latency : 0
mainboard_planning_apollo_storytelling_proc_latency_80 : 0
mainboard_planning_apollo_storytelling_proc_latency_90 : 0
mainboard_planning_apollo_storytelling_proc_latency_99 : 0
mainboard_planning_apollo_storytelling_proc_latency_999 : 0
mainboard_planning_apollo_storytelling_proc_latency_9999 : 0
mainboard_planning_apollo_storytelling_proc_max_latency : 0
mainboard_planning_apollo_storytelling_tran_latency : 0
mainboard_planning_apollo_storytelling_tran_latency_80 : 0
mainboard_planning_apollo_storytelling_tran_latency_90 : 0
mainboard_planning_apollo_storytelling_tran_latency_99 : 0
mainboard_planning_apollo_storytelling_tran_latency_999 : 0
mainboard_planning_apollo_storytelling_tran_latency_9999 : 0
mainboard_planning_apollo_storytelling_tran_max_latency : 0
