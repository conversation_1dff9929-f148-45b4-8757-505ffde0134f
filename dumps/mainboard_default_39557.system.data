mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/studio_connector/studio_connector.dag
-p
mainboard_default_39557
-s
CYBER_DEFAULT
--disable_plugin_autoload
"
mainboard_process_context_switches_involuntary_second : 6
mainboard_process_context_switches_voluntary_second : 10585
mainboard_process_cpu_usage : 0.047
mainboard_process_cpu_usage_system : 0.030
mainboard_process_cpu_usage_user : 0.016
mainboard_process_disk_read_bytes_second : 0
mainboard_process_disk_write_bytes_second : 2048
mainboard_process_faults_major : 221
mainboard_process_faults_minor_second : 55
mainboard_process_fd_count : 117
mainboard_process_inblocks_second : 0
mainboard_process_io_read_bytes_second : 2672
mainboard_process_io_read_second : 8
mainboard_process_io_write_bytes_second : 619
mainboard_process_io_write_second : 1
mainboard_process_memory_data_and_stack : 1618751488
mainboard_process_memory_resident : 426102784
mainboard_process_memory_shared : 174534656
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 2060697600
mainboard_process_nice : 0
mainboard_process_outblocks_second : 4
mainboard_process_priority : 20
mainboard_process_thread_count : 164
mainboard_process_uptime : 910.101611
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
