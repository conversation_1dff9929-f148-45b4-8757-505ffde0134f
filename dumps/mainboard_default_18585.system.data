mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/studio_connector/studio_connector.dag
-p
mainboard_default_18585
-s
CYBER_DEFAULT
--disable_plugin_autoload
"
mainboard_process_context_switches_involuntary_second : 13
mainboard_process_context_switches_voluntary_second : 10390
mainboard_process_cpu_usage : 0.052
mainboard_process_cpu_usage_system : 0.032
mainboard_process_cpu_usage_user : 0.020
mainboard_process_disk_read_bytes_second : 17202
mainboard_process_disk_write_bytes_second : 2048
mainboard_process_faults_major : 365
mainboard_process_faults_minor_second : 6
mainboard_process_fd_count : 111
mainboard_process_inblocks_second : 34
mainboard_process_io_read_bytes_second : 533
mainboard_process_io_read_second : 5
mainboard_process_io_write_bytes_second : 593
mainboard_process_io_write_second : 1
mainboard_process_memory_data_and_stack : 577515520
mainboard_process_memory_resident : 23732224
mainboard_process_memory_shared : 12001280
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 1018953728
mainboard_process_nice : 0
mainboard_process_outblocks_second : 4
mainboard_process_priority : 20
mainboard_process_thread_count : 38
mainboard_process_uptime : 7230.779310
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
