mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/studio_connector/studio_connector.dag
-p
mainboard_default_52176
-s
CYBER_DEFAULT
--disable_plugin_autoload
"
mainboard_process_context_switches_involuntary_second : 14
mainboard_process_context_switches_voluntary_second : 10414
mainboard_process_cpu_usage : 0.059
mainboard_process_cpu_usage_system : 0.040
mainboard_process_cpu_usage_user : 0.019
mainboard_process_disk_read_bytes_second : 3075961
mainboard_process_disk_write_bytes_second : 1638
mainboard_process_faults_major : 1981
mainboard_process_faults_minor_second : 169
mainboard_process_fd_count : 13
mainboard_process_inblocks_second : 6008
mainboard_process_io_read_bytes_second : 533
mainboard_process_io_read_second : 5
mainboard_process_io_write_bytes_second : 540
mainboard_process_io_write_second : 0
mainboard_process_memory_data_and_stack : 416038912
mainboard_process_memory_resident : 53813248
mainboard_process_memory_shared : 38723584
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 857399296
mainboard_process_nice : 0
mainboard_process_outblocks_second : 3
mainboard_process_priority : 20
mainboard_process_thread_count : 17
mainboard_process_uptime : 15866.621705
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
