mainboard_cpu_usage_latency : 6
mainboard_cpu_usage_latency_80 : 6
mainboard_cpu_usage_latency_90 : 6
mainboard_cpu_usage_latency_99 : 6
mainboard_cpu_usage_latency_999 : 6
mainboard_cpu_usage_latency_9999 : 6
mainboard_cpu_usage_max_latency : 6
mainboard_mem_resident_usage_latency : 902100
mainboard_mem_resident_usage_latency_80 : 902100
mainboard_mem_resident_usage_latency_90 : 902100
mainboard_mem_resident_usage_latency_99 : 902100
mainboard_mem_resident_usage_latency_999 : 902100
mainboard_mem_resident_usage_latency_9999 : 902100
mainboard_mem_resident_usage_max_latency : 902100
mainboard_prediction_apollo_localization_pose_cyber_latency : 0
mainboard_prediction_apollo_localization_pose_cyber_latency_80 : 0
mainboard_prediction_apollo_localization_pose_cyber_latency_90 : 0
mainboard_prediction_apollo_localization_pose_cyber_latency_99 : 0
mainboard_prediction_apollo_localization_pose_cyber_latency_999 : 0
mainboard_prediction_apollo_localization_pose_cyber_latency_9999 : 0
mainboard_prediction_apollo_localization_pose_cyber_max_latency : 0
mainboard_prediction_apollo_localization_pose_proc_latency : 0
mainboard_prediction_apollo_localization_pose_proc_latency_80 : 0
mainboard_prediction_apollo_localization_pose_proc_latency_90 : 0
mainboard_prediction_apollo_localization_pose_proc_latency_99 : 0
mainboard_prediction_apollo_localization_pose_proc_latency_999 : 0
mainboard_prediction_apollo_localization_pose_proc_latency_9999 : 0
mainboard_prediction_apollo_localization_pose_proc_max_latency : 0
mainboard_prediction_apollo_localization_pose_tran_latency : 84
mainboard_prediction_apollo_localization_pose_tran_latency_80 : 101
mainboard_prediction_apollo_localization_pose_tran_latency_90 : 113
mainboard_prediction_apollo_localization_pose_tran_latency_99 : 371
mainboard_prediction_apollo_localization_pose_tran_latency_999 : 7853
mainboard_prediction_apollo_localization_pose_tran_latency_9999 : 7853
mainboard_prediction_apollo_localization_pose_tran_max_latency : 7853
mainboard_prediction_apollo_perception_obstacles_cyber_latency : 0
mainboard_prediction_apollo_perception_obstacles_cyber_latency_80 : 0
mainboard_prediction_apollo_perception_obstacles_cyber_latency_90 : 0
mainboard_prediction_apollo_perception_obstacles_cyber_latency_99 : 0
mainboard_prediction_apollo_perception_obstacles_cyber_latency_999 : 0
mainboard_prediction_apollo_perception_obstacles_cyber_latency_9999 : 0
mainboard_prediction_apollo_perception_obstacles_cyber_max_latency : 0
mainboard_prediction_apollo_perception_obstacles_proc_latency : 0
mainboard_prediction_apollo_perception_obstacles_proc_latency_80 : 0
mainboard_prediction_apollo_perception_obstacles_proc_latency_90 : 0
mainboard_prediction_apollo_perception_obstacles_proc_latency_99 : 0
mainboard_prediction_apollo_perception_obstacles_proc_latency_999 : 0
mainboard_prediction_apollo_perception_obstacles_proc_latency_9999 : 0
mainboard_prediction_apollo_perception_obstacles_proc_max_latency : 0
mainboard_prediction_apollo_perception_obstacles_tran_latency : 0
mainboard_prediction_apollo_perception_obstacles_tran_latency_80 : 0
mainboard_prediction_apollo_perception_obstacles_tran_latency_90 : 0
mainboard_prediction_apollo_perception_obstacles_tran_latency_99 : 0
mainboard_prediction_apollo_perception_obstacles_tran_latency_999 : 0
mainboard_prediction_apollo_perception_obstacles_tran_latency_9999 : 0
mainboard_prediction_apollo_perception_obstacles_tran_max_latency : 0
mainboard_prediction_apollo_planning_cyber_latency : 0
mainboard_prediction_apollo_planning_cyber_latency_80 : 0
mainboard_prediction_apollo_planning_cyber_latency_90 : 0
mainboard_prediction_apollo_planning_cyber_latency_99 : 0
mainboard_prediction_apollo_planning_cyber_latency_999 : 0
mainboard_prediction_apollo_planning_cyber_latency_9999 : 0
mainboard_prediction_apollo_planning_cyber_max_latency : 0
mainboard_prediction_apollo_planning_proc_latency : 0
mainboard_prediction_apollo_planning_proc_latency_80 : 0
mainboard_prediction_apollo_planning_proc_latency_90 : 0
mainboard_prediction_apollo_planning_proc_latency_99 : 0
mainboard_prediction_apollo_planning_proc_latency_999 : 0
mainboard_prediction_apollo_planning_proc_latency_9999 : 0
mainboard_prediction_apollo_planning_proc_max_latency : 0
mainboard_prediction_apollo_planning_tran_latency : 0
mainboard_prediction_apollo_planning_tran_latency_80 : 0
mainboard_prediction_apollo_planning_tran_latency_90 : 0
mainboard_prediction_apollo_planning_tran_latency_99 : 0
mainboard_prediction_apollo_planning_tran_latency_999 : 0
mainboard_prediction_apollo_planning_tran_latency_9999 : 0
mainboard_prediction_apollo_planning_tran_max_latency : 0
mainboard_prediction_apollo_storytelling_cyber_latency : 0
mainboard_prediction_apollo_storytelling_cyber_latency_80 : 0
mainboard_prediction_apollo_storytelling_cyber_latency_90 : 0
mainboard_prediction_apollo_storytelling_cyber_latency_99 : 0
mainboard_prediction_apollo_storytelling_cyber_latency_999 : 0
mainboard_prediction_apollo_storytelling_cyber_latency_9999 : 0
mainboard_prediction_apollo_storytelling_cyber_max_latency : 0
mainboard_prediction_apollo_storytelling_proc_latency : 0
mainboard_prediction_apollo_storytelling_proc_latency_80 : 0
mainboard_prediction_apollo_storytelling_proc_latency_90 : 0
mainboard_prediction_apollo_storytelling_proc_latency_99 : 0
mainboard_prediction_apollo_storytelling_proc_latency_999 : 0
mainboard_prediction_apollo_storytelling_proc_latency_9999 : 0
mainboard_prediction_apollo_storytelling_proc_max_latency : 0
mainboard_prediction_apollo_storytelling_tran_latency : 0
mainboard_prediction_apollo_storytelling_tran_latency_80 : 0
mainboard_prediction_apollo_storytelling_tran_latency_90 : 0
mainboard_prediction_apollo_storytelling_tran_latency_99 : 0
mainboard_prediction_apollo_storytelling_tran_latency_999 : 0
mainboard_prediction_apollo_storytelling_tran_latency_9999 : 0
mainboard_prediction_apollo_storytelling_tran_max_latency : 0
