mainboard_kernel_version : "Linux in-dev-docker 5.15.0-118-generic #128~20.04.1-Ubuntu SMP Wed Jul 17 13:41:17 UTC 2024 x86_64 x86_64 x86_64 GNU/Linux
"
mainboard_process_cmdline : "mainboard
-d
modules/studio_connector/studio_connector.dag
-p
mainboard_default_47577
-s
CYBER_DEFAULT
--disable_plugin_autoload
"
mainboard_process_context_switches_involuntary_second : 11
mainboard_process_context_switches_voluntary_second : 10527
mainboard_process_cpu_usage : 0.043
mainboard_process_cpu_usage_system : 0.035
mainboard_process_cpu_usage_user : 0.008
mainboard_process_disk_read_bytes_second : 0
mainboard_process_disk_write_bytes_second : 1229
mainboard_process_faults_major : 200
mainboard_process_faults_minor_second : 0
mainboard_process_fd_count : 111
mainboard_process_inblocks_second : 0
mainboard_process_io_read_bytes_second : 530
mainboard_process_io_read_second : 5
mainboard_process_io_write_bytes_second : 574
mainboard_process_io_write_second : 1
mainboard_process_memory_data_and_stack : 576466944
mainboard_process_memory_resident : 311279616
mainboard_process_memory_shared : 70590464
mainboard_process_memory_text : 331776
mainboard_process_memory_virtual : 1017905152
mainboard_process_nice : 0
mainboard_process_outblocks_second : 2
mainboard_process_priority : 20
mainboard_process_thread_count : 38
mainboard_process_uptime : 2380.315151
mainboard_process_username : "unknown (No such device or address)"
mainboard_process_work_dir : "/apollo"
