#include <algorithm>  // 引入算法库，包含诸如std::reverse等算法
#include <cmath>      // 引入数学库，包含数学函数如std::fabs
#include <limits>     // 引入限制库，提供数值类型的极限值
#include <queue>      // 引入队列库，使用优先队列

// 引入Apollo路由模块相关的头文件
#include "modules/routing/common/routing_gflags.h"
#include "modules/routing/graph/sub_topo_graph.h"
#include "modules/routing/graph/topo_graph.h"
#include "modules/routing/strategy/a_star_strategy.h"

namespace apollo {   // 开始Apollo命名空间
namespace routing {  // 开始routing命名空间
namespace {          // 匿名命名空间，用于内部链接

// 辅助函数：检查边是否连接到特定的禁止车道ID
bool IsForbiddenLane(const TopoEdge* edge, const std::string& forbidden_lane_id) {
    const auto* to_node = edge->ToNode();
    return to_node->LaneId() == forbidden_lane_id;
}

// 定义搜索节点结构体，用于A*算法的优先队列
struct SearchNode {
    const TopoNode* topo_node = nullptr;            // 指向拓扑节点的指针
    double f = std::numeric_limits<double>::max();  // f值，初始化为最大双精度数

    SearchNode() = default;  // 默认构造函数
    explicit SearchNode(const TopoNode* node) :
            topo_node(node), f(std::numeric_limits<double>::max()) {}  // 带参数的构造函数
    SearchNode(const SearchNode& search_node) = default;               // 默认拷贝构造函数

    // 重载小于运算符，用于优先队列按f值从小到大排序
    bool operator<(const SearchNode& node) const {
        // 优先队列默认是最大堆，这里通过反向比较实现最小堆
        return f > node.f;
    }

    // 重载等于运算符，判断两个搜索节点是否指向同一拓扑节点
    bool operator==(const SearchNode& node) const {
        return topo_node == node.topo_node;
    }
};

// 计算从当前节点到邻居节点的代价
double GetCostToNeighbor(const TopoEdge* edge) {
    return (edge->Cost() + edge->ToNode()->Cost());  // 边的代价加上目标节点的代价
}

// 获取节点集合中范围最大的节点
const TopoNode* GetLargestNode(const std::vector<const TopoNode*>& nodes) {
    double max_range = 0.0;                                       // 初始化最大范围
    const TopoNode* largest = nullptr;                            // 指向最大范围节点的指针
    for (const auto* node : nodes) {                              // 遍历所有节点
        const double temp_range = node->EndS() - node->StartS();  // 计算节点的范围
        if (temp_range > max_range) {                             // 如果当前节点范围更大
            max_range = temp_range;                               // 更新最大范围
            largest = node;                                       // 更新最大节点指针
        }
    }
    return largest;  // 返回范围最大的节点
}

// 向后调整车道变换路径
bool AdjustLaneChangeBackward(std::vector<const TopoNode*>* const result_node_vec) {
    // 从倒数第二个节点开始，向前遍历
    for (int i = static_cast<int>(result_node_vec->size()) - 2; i > 0; --i) {
        const auto* from_node = result_node_vec->at(i);               // 当前节点
        const auto* to_node = result_node_vec->at(i + 1);             // 下一个节点
        const auto* base_node = result_node_vec->at(i - 1);           // 基准节点
        const auto* from_to_edge = from_node->GetOutEdgeTo(to_node);  // 获取从当前节点到下一个节点的边
        if (from_to_edge == nullptr) {                                // 如果边不存在
            // 可能需要重新计算边，因为只保存了从原始节点到子节点的边
            from_to_edge = to_node->GetInEdgeFrom(from_node);  // 尝试从反方向获取边
        }
        if (from_to_edge == nullptr) {  // 如果仍然获取不到边
            AERROR << "Get null ptr to edge:" << from_node->LaneId() << " (" << from_node->StartS() << ", "
                   << from_node->EndS() << ")"
                   << " --> " << to_node->LaneId() << " (" << to_node->StartS() << ", " << to_node->EndS()
                   << ")";  // 记录错误日志
            return false;   // 返回失败
        }
        if (from_to_edge->Type() != TopoEdgeType::TET_FORWARD) {  // 如果边类型不是前向
            if (base_node->EndS() - base_node->StartS()
                < from_node->EndS() - from_node->StartS()) {  // 比较基准节点和当前节点的范围
                continue;                                     // 如果基准节点范围较小，跳过调整
            }
            std::vector<const TopoNode*> candidate_set;                 // 候选节点集合
            candidate_set.push_back(from_node);                         // 添加当前节点到候选集合
            const auto& out_edges = base_node->OutToLeftOrRightEdge();  // 获取基准节点的左或右出边
            for (const auto* edge : out_edges) {                        // 遍历所有出边
                const auto* candidate_node = edge->ToNode();            // 获取边的目标节点
                if (candidate_node == from_node) {                      // 如果目标节点是当前节点
                    continue;                                           // 跳过
                }
                if (candidate_node->GetOutEdgeTo(to_node) != nullptr) {  // 如果候选节点有到下一个节点的出边
                    candidate_set.push_back(candidate_node);             // 添加到候选集合
                }
            }
            const auto* largest_node = GetLargestNode(candidate_set);  // 获取候选集合中范围最大的节点
            if (largest_node == nullptr) {                             // 如果没有找到
                return false;                                          // 返回失败
            }
            if (largest_node != from_node) {            // 如果最大的候选节点不是当前节点
                result_node_vec->at(i) = largest_node;  // 替换当前节点为最大的候选节点
            }
        }
    }
    return true;  // 调整成功
}

// 向前调整车道变换路径
bool AdjustLaneChangeForward(std::vector<const TopoNode*>* const result_node_vec) {
    // 从第二个节点开始，向后遍历到倒数第二个节点
    for (size_t i = 1; i < result_node_vec->size() - 1; ++i) {
        const auto* from_node = result_node_vec->at(i - 1);           // 前一个节点
        const auto* to_node = result_node_vec->at(i);                 // 当前节点
        const auto* base_node = result_node_vec->at(i + 1);           // 基准节点
        const auto* from_to_edge = from_node->GetOutEdgeTo(to_node);  // 获取从前一个节点到当前节点的边
        if (from_to_edge == nullptr) {                                // 如果边不存在
            // 可能需要重新计算边，因为只保存了从原始节点到子节点的边
            from_to_edge = to_node->GetInEdgeFrom(from_node);  // 尝试从反方向获取边
        }
        if (from_to_edge == nullptr) {  // 如果仍然获取不到边
            AERROR << "Get null ptr to edge:" << from_node->LaneId() << " (" << from_node->StartS() << ", "
                   << from_node->EndS() << ")"
                   << " --> " << to_node->LaneId() << " (" << to_node->StartS() << ", " << to_node->EndS()
                   << ")";  // 记录错误日志
            return false;   // 返回失败
        }
        if (from_to_edge->Type() != TopoEdgeType::TET_FORWARD) {  // 如果边类型不是前向
            if (base_node->EndS() - base_node->StartS()
                < to_node->EndS() - to_node->StartS()) {  // 比较基准节点和当前节点的范围
                continue;                                 // 如果基准节点范围较小，跳过调整
            }
            std::vector<const TopoNode*> candidate_set;                 // 候选节点集合
            candidate_set.push_back(to_node);                           // 添加当前节点到候选集合
            const auto& in_edges = base_node->InFromLeftOrRightEdge();  // 获取基准节点的左或右入边
            for (const auto* edge : in_edges) {                         // 遍历所有入边
                const auto* candidate_node = edge->FromNode();          // 获取边的来源节点
                if (candidate_node == to_node) {                        // 如果来源节点是当前节点
                    continue;                                           // 跳过
                }
                if (candidate_node->GetInEdgeFrom(from_node) != nullptr) {  // 如果候选节点有从前一个节点的入边
                    candidate_set.push_back(candidate_node);                // 添加到候选集合
                }
            }
            const auto* largest_node = GetLargestNode(candidate_set);  // 获取候选集合中范围最大的节点
            if (largest_node == nullptr) {                             // 如果没有找到
                return false;                                          // 返回失败
            }
            if (largest_node != to_node) {              // 如果最大的候选节点不是当前节点
                result_node_vec->at(i) = largest_node;  // 替换当前节点为最大的候选节点
            }
        }
    }
    return true;  // 调整成功
}

// 调整车道变换路径，包括向前和向后调整
bool AdjustLaneChange(std::vector<const TopoNode*>* const result_node_vec) {
    if (result_node_vec->size() < 3) {  // 如果路径节点少于3个
        return true;                    // 无需调整，直接返回成功
    }
    if (!AdjustLaneChangeBackward(result_node_vec)) {       // 尝试向后调整
        AERROR << "Failed to adjust lane change backward";  // 记录错误日志
        return false;                                       // 返回失败
    }
    if (!AdjustLaneChangeForward(result_node_vec)) {        // 尝试向前调整
        AERROR << "Failed to adjust lane change backward";  // 记录错误日志（应为forward）
        return false;                                       // 返回失败
    }
    return true;  // 调整成功
}

// 重构路径，将came_from映射转换为路径节点序列
bool Reconstruct(
        const std::unordered_map<const TopoNode*, const TopoNode*>& came_from,
        const TopoNode* dest_node,
        std::vector<NodeWithRange>* result_nodes) {
    std::vector<const TopoNode*> result_node_vec;  // 初始化结果节点向量
    result_node_vec.push_back(dest_node);          // 将目标节点加入结果

    auto iter = came_from.find(dest_node);        // 查找目标节点的前驱节点
    while (iter != came_from.end()) {             // 当找到前驱节点时
        result_node_vec.push_back(iter->second);  // 将前驱节点加入结果
        iter = came_from.find(iter->second);      // 继续查找前驱节点
    }
    std::reverse(result_node_vec.begin(), result_node_vec.end());  // 反转结果节点序列，使之从起点到终点
    if (!AdjustLaneChange(&result_node_vec)) {                     // 调整车道变换路径
        AERROR << "Failed to adjust lane change";                  // 记录错误日志
        return false;                                              // 返回失败
    }
    result_nodes->clear();                      // 清空输出结果
    for (const auto* node : result_node_vec) {  // 遍历结果节点
        result_nodes->emplace_back(node->OriginNode(), node->StartS(),
                                   node->EndS());  // 将节点及其范围加入输出结果
    }
    return true;  // 重构成功
}

}  // namespace

// AStarStrategy类的构造函数，初始化车道变换是否启用
AStarStrategy::AStarStrategy(bool enable_change) : change_lane_enabled_(enable_change) {}

// 清空A*算法使用的所有数据结构
void AStarStrategy::Clear() {
    closed_set_.clear();  // 清空已关闭集合
    open_set_.clear();    // 清空开启集合
    came_from_.clear();   // 清空前驱节点映射
    enter_s_.clear();     // 清空进入s值映射
    g_score_.clear();     // 清空g值映射
}

// 计算启发式代价，使用曼哈顿距离
double AStarStrategy::HeuristicCost(const TopoNode* src_node, const TopoNode* dest_node) {
    const auto& src_point = src_node->AnchorPoint();    // 获取源节点锚点
    const auto& dest_point = dest_node->AnchorPoint();  // 获取目标节点锚点
    double distance
            = std::fabs(src_point.x() - dest_point.x()) + std::fabs(src_point.y() - dest_point.y());  // 计算曼哈顿距离
    return distance;                                                                                  // 返回启发式代价
}

// A*搜索算法的主函数
bool AStarStrategy::Search(
        const TopoGraph* graph,
        const SubTopoGraph* sub_graph,
        const TopoNode* src_node,
        const TopoNode* dest_node,
        std::vector<NodeWithRange>* const result_nodes) {
    Clear();                                // 清空之前的搜索数据
    AINFO << "Start A* search algorithm.";  // 记录信息日志

    std::priority_queue<SearchNode> open_set_detail;  // 定义优先队列作为开启集合的详细表示

    SearchNode src_search_node(src_node);                    // 创建源节点的搜索节点
    src_search_node.f = HeuristicCost(src_node, dest_node);  // 计算源节点的f值
    open_set_detail.push(src_search_node);                   // 将源节点加入优先队列

    open_set_.insert(src_node);               // 将源节点加入开启集合
    g_score_[src_node] = 0.0;                 // 源节点的g值为0
    enter_s_[src_node] = src_node->StartS();  // 记录源节点的进入s值

    SearchNode current_node;                             // 当前节点
    std::unordered_set<const TopoEdge*> next_edge_set;   // 下一步边集合
    std::unordered_set<const TopoEdge*> sub_edge_set;    // 子图边集合
    while (!open_set_detail.empty()) {                   // 当开启集合不为空时
        current_node = open_set_detail.top();            // 获取f值最小的节点
        const auto* from_node = current_node.topo_node;  // 当前节点

        AINFO << "当前ADC所在（正在搜索的）车道ID: " << from_node->LaneId();  // 打印当前处理节点的车道ID

        if (current_node.topo_node == dest_node) {                    // 如果当前节点是目标节点
            if (!Reconstruct(came_from_, from_node, result_nodes)) {  // 重构路径
                AERROR << "Failed to reconstruct route.";             // 记录错误日志
                return false;                                         // 返回失败
            }
            return true;  // 搜索成功
        }
        open_set_.erase(from_node);  // 从开启集合中移除当前节点
        open_set_detail.pop();       // 从优先队列中移除当前节点

        if (closed_set_.count(from_node) != 0) {  // 如果当前节点已在关闭集合中
            // 如果之前已经处理过，跳过
            continue;
        }
        closed_set_.emplace(from_node);  // 将当前节点加入关闭集合

        // 判断剩余长度是否足够进行车道变换，并且车道变换是否启用
        const auto& neighbor_edges
                = (GetResidualS(from_node) > FLAGS_min_length_for_lane_change && change_lane_enabled_)
                ? from_node->OutToAllEdge()                                  // 如果可以变道，则获取所有出边
                : from_node->OutToSucEdge();                                 // 否则只获取顺行出边
        double tentative_g_score = 0.0;                                      // 暂定的g值
        next_edge_set.clear();                                               // 清空下一步边集合
        for (const auto* edge : neighbor_edges) {                            // 遍历所有邻居边
            sub_edge_set.clear();                                            // 清空子图边集合
            sub_graph->GetSubInEdgesIntoSubGraph(edge, &sub_edge_set);       // 获取子图中的入边
            next_edge_set.insert(sub_edge_set.begin(), sub_edge_set.end());  // 将子图边加入下一步边集合
        }

        for (const auto* edge : next_edge_set) {  // 遍历所有下一步边
            // 检查当前边是否为有障碍物的禁止通行车道
            if (IsForbiddenLane(edge, "Lane_1xXX")) {
                AINFO << "跳过禁止通行车道 " << edge->FromNode()->LaneId() << " --> " << edge->ToNode()->LaneId();
                continue;  // 跳过禁止车道的边
            }
            const auto* to_node = edge->ToNode();   // 获取目标节点
            if (closed_set_.count(to_node) == 1) {  // 如果目标节点已在关闭集合中
                continue;                           // 跳过
            }
            if (GetResidualS(edge, to_node) < FLAGS_min_length_for_lane_change) {  // 如果剩余长度不足以变道
                continue;                                                          // 跳过
            }
            tentative_g_score = g_score_[current_node.topo_node] + GetCostToNeighbor(edge);    // 计算暂定的g值
            if (edge->Type() != TopoEdgeType::TET_FORWARD) {                                   // 如果边类型不是前向
                tentative_g_score -= (edge->FromNode()->Cost() + edge->ToNode()->Cost()) / 2;  // 调整g值
            }
            double f = tentative_g_score + HeuristicCost(to_node, dest_node);  // 计算f值
            if (open_set_.count(to_node) != 0 && f >= g_score_[to_node]) {     // 如果目标节点已在开启集合中且f值不更小
                continue;                                                      // 跳过
            }
            // 如果通过前向边到达目标节点，重置进入s值为起始s值
            if (edge->Type() == TopoEdgeType::TET_FORWARD) {
                enter_s_[to_node] = to_node->StartS();
            } else {
                // 否则，增加进入s值，确保变道长度
                double to_node_enter_s = (enter_s_[from_node] + FLAGS_min_length_for_lane_change) / from_node->Length()
                        * to_node->Length();
                // 进入s值不应超过节点长度
                to_node_enter_s = std::min(to_node_enter_s, to_node->Length());
                // 如果进入s值超过结束s值且目标节点是终点，跳过
                if (to_node_enter_s > to_node->EndS() && to_node == dest_node) {
                    continue;
                }
                enter_s_[to_node] = to_node_enter_s;  // 更新进入s值
            }

            g_score_[to_node] = f;                // 更新g值
            SearchNode next_node(to_node);        // 创建下一个搜索节点
            next_node.f = f;                      // 设置f值
            open_set_detail.push(next_node);      // 将下一个节点加入优先队列
            came_from_[to_node] = from_node;      // 记录前驱节点
            if (open_set_.count(to_node) == 0) {  // 如果目标节点不在开启集合中
                open_set_.insert(to_node);        // 将其加入开启集合
            }
        }
    }
    AERROR << "Failed to find goal lane with id: " << dest_node->LaneId();  // 记录错误日志
    return false;                                                           // 搜索失败
}

// 获取节点的剩余s值
double AStarStrategy::GetResidualS(const TopoNode* node) {
    double start_s = node->StartS();        // 获取节点的起始s值
    const auto iter = enter_s_.find(node);  // 查找进入s值映射
    if (iter != enter_s_.end()) {           // 如果找到
        if (iter->second > node->EndS()) {  // 如果进入s值超过结束s值
            return 0.0;                     // 剩余s值为0
        }
        start_s = iter->second;  // 更新起始s值为进入s值
    } else {                     // 如果未找到
        AWARN << "lane " << node->LaneId() << "(" << node->StartS() << ", " << node->EndS()
              << "not found in enter_s map";  // 记录警告日志
    }
    double end_s = node->EndS();                           // 获取节点的结束s值
    const TopoNode* succ_node = nullptr;                   // 后继节点指针
    for (const auto* edge : node->OutToAllEdge()) {        // 遍历所有出边
        if (edge->ToNode()->LaneId() == node->LaneId()) {  // 如果出边的目标节点车道ID与当前节点相同
            succ_node = edge->ToNode();                    // 设置后继节点
            break;                                         // 退出循环
        }
    }
    if (succ_node != nullptr) {     // 如果找到后继节点
        end_s = succ_node->EndS();  // 更新结束s值为后继节点的结束s值
    }
    return (end_s - start_s);  // 返回剩余s值
}

// 获取通过边到目标节点的剩余s值
double AStarStrategy::GetResidualS(const TopoEdge* edge, const TopoNode* to_node) {
    if (edge->Type() == TopoEdgeType::TET_FORWARD) {  // 如果边类型是前向
        return std::numeric_limits<double>::max();    // 返回无限大，表示无限剩余
    }
    double start_s = to_node->StartS();                                          // 获取目标节点的起始s值
    const auto* from_node = edge->FromNode();                                    // 获取边的来源节点
    const auto iter = enter_s_.find(from_node);                                  // 查找来源节点的进入s值
    if (iter != enter_s_.end()) {                                                // 如果找到
        double temp_s = iter->second / from_node->Length() * to_node->Length();  // 计算临时s值
        start_s = std::max(start_s, temp_s);                                     // 更新起始s值为较大值
    } else {                                                                     // 如果未找到
        AWARN << "lane " << from_node->LaneId() << "(" << from_node->StartS() << ", " << from_node->EndS()
              << "not found in enter_s map";  // 记录警告日志
    }
    double end_s = to_node->EndS();                           // 获取目标节点的结束s值
    const TopoNode* succ_node = nullptr;                      // 后继节点指针
    for (const auto* edge : to_node->OutToAllEdge()) {        // 遍历目标节点的所有出边
        if (edge->ToNode()->LaneId() == to_node->LaneId()) {  // 如果出边的目标节点车道ID与当前节点相同
            succ_node = edge->ToNode();                       // 设置后继节点
            break;                                            // 退出循环
        }
    }
    if (succ_node != nullptr) {     // 如果找到后继节点
        end_s = succ_node->EndS();  // 更新结束s值为后继节点的结束s值
    }
    return (end_s - start_s);  // 返回剩余s值
}
}  // namespace routing
}  // namespace apollo