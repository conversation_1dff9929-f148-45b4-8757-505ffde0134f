## Auto generated by `proto_build_generator.py`
load("//tools:apollo_package.bzl", "apollo_package")
load("//tools/proto:proto.bzl", "proto_library")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "topo_graph_proto",
    srcs = ["topo_graph.proto"],
    deps = [
        "//modules/common_msgs/map_msgs:map_geometry_proto",
    ],
)

proto_library(
    name = "routing_config_proto",
    srcs = ["routing_config.proto"],
)

apollo_package()