syntax = "proto2";

package apollo.routing;

message TopicConfig {
  optional string routing_response_topic = 1;
  optional string routing_response_history_topic = 2;
}

message RoutingConfig {
  optional double base_speed = 1;  // base speed for node creator [m/s]
  optional double left_turn_penalty =
      2;  // left turn penalty for node creator [m]
  optional double right_turn_penalty =
      3;                              // right turn penalty for node creator [m]
  optional double uturn_penalty = 4;  // left turn penalty for node creator [m]
  optional double change_penalty = 5;  // change penalty for edge creator [m]
  optional double base_changing_length =
      6;  // base change length penalty for edge creator [m]
  optional TopicConfig topic_config = 7;
}
