<package format="2">
  <name>routing</name>
  <version>local</version>
  <description>
    The Routing module generates high level navigation information based on requests.
    The Routing module depends on a routing topology file, usually named `routing_map.*` in apollo.
  </description>

  <maintainer email="<EMAIL>">Apollo</maintainer>
  <license>Apache License 2.0</license>
  <url type="website">https://www.apollo.auto/</url>
  <url type="repository">https://github.com/ApolloAuto/apollo</url>
  <url type="bugtracker">https://github.com/ApolloAuto/apollo/issues</url>

  <type>module</type>
  <depend repo_name="com_github_gflags_gflags" lib_names="gflags">3rd-gflags</depend>
  <depend type="binary" repo_name="common-msgs" lib_names="common-msgs">common-msgs</depend>
  <depend type="binary" repo_name="common" lib_names="common">common</depend>
  <depend type="binary" repo_name="map" lib_names="map">map</depend>
  <depend type="binary" repo_name="cyber">cyber</depend>
  <depend repo_name="com_google_googletest" lib_names="gtest,gtest_main">3rd-gtest</depend>

  <depend expose="False">3rd-rules-python</depend>
  <depend expose="False">3rd-grpc</depend>
  <depend expose="False">3rd-bazel-skylib</depend>
  <depend expose="False">3rd-rules-proto</depend>
  <depend expose="False">3rd-py</depend>
  <src_path url="https://github.com/ApolloAuto/apollo">//modules/routing</src_path>

</package>
