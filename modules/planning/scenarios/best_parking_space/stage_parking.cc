#include "modules/planning/scenarios/best_parking_space/stage_parking.h"  // 引入停车阶段头文件
#include "modules/planning/planning_base/common/frame.h"  // 引入帧头文件

namespace apollo {  // 命名空间apollo
namespace planning {  // 命名空间planning

StageResult StageBestParking::Process(const common::TrajectoryPoint& planning_init_point, Frame* frame) {  // 处理函数
    AERROR << "1";  // 错误信息：1
    // 开放空间规划不使用来自上游的planning_init_point，因为不同的拼接策略
    auto scenario_context = GetContextAs<BestParkingSpaceContext>();  // 获取场景上下文
    frame->mutable_open_space_info()->set_is_on_open_space_trajectory(true);  // 设置在开放空间轨迹上
    *(frame->mutable_open_space_info()->mutable_target_parking_spot_id()) = scenario_context->target_parking_spot_id;  // 设置目标停车位ID
    StageResult result = ExecuteTaskOnOpenSpace(frame);  // 在开放空间上执行任务
    if (result.HasError()) {  // 如果结果有错误
        AERROR << "StageParking planning error";  // 错误信息：停车阶段规划错误
        return result.SetStageStatus(StageStatusType::ERROR);  // 返回错误状态
    }
    return result.SetStageStatus(StageStatusType::RUNNING);  // 返回运行中状态
}

StageResult StageBestParking::FinishStage() {  // 完成阶段函数
    return StageResult(StageStatusType::FINISHED);  // 返回完成状态
}

}  // namespace planning
}  // namespace apollo
