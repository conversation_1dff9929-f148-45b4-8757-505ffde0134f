syntax = "proto2";

package apollo.planning;

message BestParkingSpaceConfig {
      // Only when the distance between the vehicle and parking spot is smaller 
  // than this value, "ValetParkingScenario" can be entered
  // 只有当主车和停车点的距离小于这个值时，最优停车场景才会进入
  optional double parking_spot_range_to_start = 1 [default = 20.0];
  // Only when the distance between the vehicle and pre-stop distance is smaller
  // than this value, "StageApproachingParkingSpot" can be finished
  // 只有当车辆与待停车距离之间的距离小于此值时，"StageApproachingParkingSpot" 阶段才能完成。
  optional double max_valid_stop_distance = 2 [default = 1.0];  // meter
}