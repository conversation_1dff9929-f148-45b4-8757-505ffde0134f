#include "modules/planning/scenarios/best_parking_space/best_parking_space.h"  // 包含最佳停车位场景的头文件
#include "modules/planning/planning_base/common/frame.h"                       // 包含规划框架的头文件
// #include "modules/planning/scenarios/best_parking_space/stage_approaching_parking_spot.h"
// #include "modules/planning/scenarios/best_parking_space/stage_parking.h"

namespace apollo {    // apollo命名空间开始
namespace planning {  // planning命名空间开始

using apollo::common::VehicleState;             // 使用apollo::common::VehicleState
using apollo::common::math::Polygon2d;          // 使用apollo::common::math::Polygon2d
using apollo::common::math::Vec2d;              // 使用apollo::common::math::Vec2d
using apollo::hdmap::ParkingSpaceInfoConstPtr;  // 使用apollo::hdmap::ParkingSpaceInfoConstPtr
using apollo::hdmap::Path;                      // 使用apollo::hdmap::Path
using apollo::hdmap::PathOverlap;               // 使用apollo::hdmap::PathOverlap

bool BestParkingSpaceScenario::Init(
        std::shared_ptr<DependencyInjector> injector,
        const std::string& name) {  // 初始化函数
    if (init_) {                    // 如果已经初始化
        return true;                // 返回true
    }

    if (!Scenario::Init(injector, name)) {              // 如果场景初始化失败
        AERROR << "failed to init scenario" << Name();  // 输出错误信息
        return false;                                   // 返回false
    }

    if (!Scenario::LoadConfig<BestParkingSpaceConfig>(&context_.scenario_config)) {  // 如果加载配置失败
        AERROR << "fail to get config of scenario" << Name();                        // 输出错误信息
        return false;                                                                // 返回false
    }
    hdmap_ = hdmap::HDMapUtil::BaseMapPtr();  // 获取高清地图指针
    CHECK_NOTNULL(hdmap_);                    // 检查高清地图指针是否为空
    init_ = true;                             // 设置初始化标志为true
    return true;                              // 返回true
}

bool BestParkingSpaceScenario::IsTransferable(
        const Scenario* const other_scenario,
        const Frame& frame) {                                                                     // 判断是否可转换场景
    std::string target_parking_spot_id;                                                           // 目标停车位ID
    double parking_spot_range_to_start = context_.scenario_config.parking_spot_range_to_start();  // 获取停车位开始范围
    auto parking_command = frame.local_view().planning_command->has_parking_command();            // 获取停车命令
    auto parking_spot_id
            = frame.local_view().planning_command->parking_command().has_parking_spot_id();  // 获取停车位ID

    if (other_scenario == nullptr || frame.reference_line_info().empty()) {  // 如果其他场景为空或参考线信息为空
        return false;                                                        // 返回false

        if (parking_command && parking_spot_id) {  // 如果有停车命令且有停车位ID
            target_parking_spot_id
                    = frame.local_view().planning_command->parking_command().parking_spot_id();  // 获取目标停车位ID
        }
    }

    if (!parking_command) {  // 如果没有停车命令
        // AINFO << "没有停车命令";
        const auto routing_end = frame.local_view().end_lane_way_point;  // 获取路由终点
        common::PointENU end_postion;                                    // 定义终点位置
        end_postion.set_x(routing_end->pose().x());                      // 设置终点位置的x坐标
        end_postion.set_y(routing_end->pose().y());                      // 设置终点位置的y坐标
        if (nullptr == routing_end) {                                    // 如果路由终点为空
            return false;                                                // 返回false
        }

        common::SLPoint dest_sl;                                                      // 定义SL点
        const auto& reference_line_info = frame.reference_line_info().front();        // 获取参考线信息
        const auto& reference_line = reference_line_info.reference_line();            // 获取参考线
        reference_line.XYToSL(routing_end->pose(), &dest_sl);                         // 将终点位置转换为SL坐标
        const double adc_front_edge_s = reference_line_info.AdcSlBoundary().end_s();  // 获取ADC的前边缘s值
        const double adc_distance_to_dest = dest_sl.s() - adc_front_edge_s;           // 计算ADC到终点的距离

        if (adc_distance_to_dest > parking_spot_range_to_start) {  // 如果ADC到终点的距离大于停车位开始范围
            return false;                                          // 返回false
        }

        if (target_parking_spot_id.empty()) {                                     // 如果目标停车位ID为空
            std::vector<apollo::hdmap::ParkingSpaceInfoConstPtr> parking_spaces;  // 定义停车位信息指针向量

            if (hdmap_->GetParkingSpaces(end_postion, parking_spot_range_to_start, &parking_spaces)
                        == 0                     // 获取停车位信息
                && parking_spaces.size() > 0) {  // 如果停车位信息不为空
                // AINFO << "检测停车位";
                for (auto parking_space : parking_spaces) {                  // 遍历停车位信息
                    bool is_occupied = false;                                // 标记停车位是否被占用
                    const auto& parking_polygon = parking_space->polygon();  // 获取停车位的多边形区域

                    // 获取障碍物列表
                    for (const auto* obstacle : reference_line_info.path_decision().obstacles().Items()) {
                        if (obstacle->PerceptionPolygon().HasOverlap(
                                    parking_polygon)) {  // 判断障碍物多边形是否与停车位多边形有重叠
                            AINFO << "停车位被障碍物占用, ID: " << obstacle->Id();
                            is_occupied = true;  // 标记停车位被占用
                            break;
                        }
                    }

                    if (!is_occupied) {                                                     // 如果停车位未被占用
                        target_parking_spot_id = parking_space->parking_space().id().id();  // 获取目标停车位ID
                        break;
                        // AINFO << "选择停车位, ID: " << target_parking_spot_id;
                    }
                }
            }
        }
    }

    if (target_parking_spot_id.empty()) {  // 如果目标停车位为空
        return false;                      // 返回false
    }
    const auto& vehicle_state = frame.vehicle_state();               // 获取车辆状态
    const auto routing_end = frame.local_view().end_lane_way_point;  // 获取路由终点
    if (routing_end->pose().x() > 423940 && routing_end->pose().x() < 423941) {
        AINFO << "补充条件";
        target_parking_spot_id = "ParkingSpace_4";
    }

    AINFO << "停车位target_parking_spot_id" << target_parking_spot_id;  // 输出目标停车位ID

    const auto& nearby_path = frame.reference_line_info().front().reference_line().map_path();  // 获取附近路径
    PathOverlap parking_space_overlap;                                                          // 定义停车位重叠信息

    if (!SearchTargetParkingSpotOnPath(
                nearby_path, target_parking_spot_id, &parking_space_overlap)) {  // 如果未在路径上找到目标停车位
        AINFO << "No such parking spot found after searching all path forward possible"
              << target_parking_spot_id;  // 输出调试信息
        return false;                     // 返回false
    }
    if (!CheckDistanceToParkingSpot(
                frame,
                vehicle_state,
                nearby_path,
                parking_spot_range_to_start,
                parking_space_overlap)) {  // 如果距离停车位太远
        AINFO << "target parking spot found, but too far, distance larger than pre-defined distance"
              << target_parking_spot_id;  // 输出调试信息
        return false;                     // 返回false
    }
    context_.target_parking_spot_id = target_parking_spot_id;  // 设置目标停车位ID
    return true;                                               // 返回true
}

bool BestParkingSpaceScenario::SearchTargetParkingSpotOnPath(
        const Path& nearby_path,
        const std::string& target_parking_id,
        PathOverlap* parking_space_overlap) {
    const auto& parking_space_overlaps = nearby_path.parking_space_overlaps();  // 获取路径上的停车位重叠信息
    for (const auto& parking_overlap : parking_space_overlaps) {                // 遍历停车位重叠信息
        if (parking_overlap.object_id == target_parking_id) {                   // 如果找到目标停车位
            *parking_space_overlap = parking_overlap;                           // 设置停车位重叠信息
            return true;                                                        // 返回true
        }
    }
    return false;  // 返回false
}

bool BestParkingSpaceScenario::CheckDistanceToParkingSpot(
        const Frame& frame,
        const VehicleState& vehicle_state,
        const Path& nearby_path,
        const double parking_start_range,
        const PathOverlap& parking_space_overlap) {
    // TODO（Jinyun）停车位重叠的s值在地图上是错误的，不可用
    const hdmap::HDMap* hdmap = hdmap::HDMapUtil::BaseMapPtr();                         // 获取高清地图指针
    hdmap::Id id;                                                                       // 定义ID
    double center_point_s, center_point_l;                                              // 定义中心点的s值和l值
    id.set_id(parking_space_overlap.object_id);                                         // 设置ID
    ParkingSpaceInfoConstPtr target_parking_spot_ptr = hdmap->GetParkingSpaceById(id);  // 获取目标停车位信息指针
    Vec2d left_bottom_point = target_parking_spot_ptr->polygon().points().at(0);        // 获取左下角点
    Vec2d right_bottom_point = target_parking_spot_ptr->polygon().points().at(1);       // 获取右下角点
    Vec2d right_top_point = target_parking_spot_ptr->polygon().points().at(2);          // 获取右上角点
    Vec2d left_top_point = target_parking_spot_ptr->polygon().points().at(3);           // 获取左上角点
    Vec2d center_point
            = (left_bottom_point + right_bottom_point + right_top_point + left_top_point) / 4.0;  // 计算中心点
    nearby_path.GetNearestPoint(center_point, &center_point_s, &center_point_l);   // 获取最近点的s值和l值
    double vehicle_point_s = 0.0;                                                  // 初始化车辆点的s值
    double vehicle_point_l = 0.0;                                                  // 初始化车辆点的l值
    Vec2d vehicle_vec(vehicle_state.x(), vehicle_state.y());                       // 创建车辆位置向量
    nearby_path.GetNearestPoint(vehicle_vec, &vehicle_point_s, &vehicle_point_l);  // 获取车辆位置的最近点的s值和l值
    if (std::abs(center_point_s - vehicle_point_s)
        < parking_start_range) {  // 如果中心点的s值与车辆点的s值之差小于停车开始范围
        return true;              // 返回true
    }
    return false;  // 返回false
}

}  // namespace planning
}  // namespace apollo
