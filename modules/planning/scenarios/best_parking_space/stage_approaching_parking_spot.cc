#include <string>                                                                          // 引入字符串库
#include "modules/common/configs/vehicle_config_helper.h"                                  // 引入车辆配置助手头文件
#include "modules/common/vehicle_state/vehicle_state_provider.h"                           // 引入车辆状态提供者头文件
#include "modules/planning/scenarios/best_parking_space/stage_approaching_parking_spot.h"  // 引入接近停车位阶段头文件

namespace apollo {    // 命名空间apollo
namespace planning {  // 命名空间planning
bool StageBestApproachingParkingSpot::
        Init(                                                         // 初始化函数
                const StagePipeline& config,                          // 配置参数
                const std::shared_ptr<DependencyInjector>& injector,  // 依赖注入器
                const std::string& config_dir,                        // 配置目录
                void* context) {                                      // 上下文
    if (!Stage::Init(config, injector, config_dir, context)) {        // 调用基类的初始化函数
        return false;                                                 // 如果失败，返回false
    }
    scenario_config_.CopyFrom(GetContextAs<BestParkingSpaceContext>()->scenario_config);  // 从上下文复制场景配置
    return true;                                                                          // 初始化成功，返回true
}
StageResult StageBestApproachingParkingSpot::Process(
        const common::TrajectoryPoint& planning_init_point,
        Frame* frame) {                                               // 处理函数
    ADEBUG << "stage: StageBestApproachingParkingSpot";               // 调试信息
    CHECK_NOTNULL(frame);                                             // 检查frame不为空
    StageResult result;                                               // 定义阶段结果
    auto scenario_context = GetContextAs<BestParkingSpaceContext>();  // 获取场景上下文

    if (scenario_context->target_parking_spot_id.empty()) {    // 如果目标停车位ID为空
        return result.SetStageStatus(StageStatusType::ERROR);  // 返回错误状态
    }

    *(frame->mutable_open_space_info()->mutable_target_parking_spot_id())
            = scenario_context->target_parking_spot_id;  // 设置目标停车位ID
    frame->mutable_open_space_info()->set_pre_stop_rightaway_flag(
            scenario_context->pre_stop_rightaway_flag);                      // 设置预停车标志
    *(frame->mutable_open_space_info()->mutable_pre_stop_rightaway_point())  // 设置预停车点
            = scenario_context->pre_stop_rightaway_point;
    auto* reference_lines = frame->mutable_reference_line_info();  // 获取参考线信息
    for (auto& reference_line : *reference_lines) {                // 遍历参考线
        auto* path_decision = reference_line.path_decision();      // 获取路径决策
        if (nullptr == path_decision) {                            // 如果路径决策为空
            continue;                                              // 继续下一个循环
        }
        auto* dest_obstacle = path_decision->Find(FLAGS_destination_obstacle_id);  // 找到目标障碍物
        if (nullptr == dest_obstacle) {                                            // 如果目标障碍物为空
            continue;                                                              // 继续下一个循环
        }
        ObjectDecisionType decision;                                                       // 定义对象决策类型
        decision.mutable_ignore();                                                         // 忽略决策
        dest_obstacle->EraseDecision();                                                    // 擦除决策
        dest_obstacle->AddLongitudinalDecision("ignore-dest-in-valet-parking", decision);  // 添加纵向决策
    }
    result = ExecuteTaskOnReferenceLine(planning_init_point, frame);  // 在参考线上执行任务

    scenario_context->pre_stop_rightaway_flag = frame->open_space_info().pre_stop_rightaway_flag();    // 更新预停车标志
    scenario_context->pre_stop_rightaway_point = frame->open_space_info().pre_stop_rightaway_point();  // 更新预停车点

    if (CheckADCStop(*frame)) {                         // 检查ADC是否停止
        next_stage_ = "BEST_PARKING_PARKING";           // 设置下一阶段为停车
        return StageResult(StageStatusType::FINISHED);  // 返回完成状态
    }
    if (result.HasError()) {                                         // 如果结果有错误
        AERROR << "StopSignUnprotectedStagePreStop planning error";  // 错误信息
        return result.SetStageStatus(StageStatusType::ERROR);        // 返回错误状态
    }

    return result.SetStageStatus(StageStatusType::RUNNING);  // 返回运行中状态
}

bool StageBestApproachingParkingSpot::CheckADCStop(const Frame& frame) {     // 检查ADC停止函数
    const auto& reference_line_info = frame.reference_line_info().front();   // 获取参考线信息
    const double adc_speed = injector_->vehicle_state()->linear_velocity();  // 获取车辆速度
    const double max_adc_stop_speed                                          // 获取最大停车速度
            = common::VehicleConfigHelper::Instance()->GetConfig().vehicle_param().max_abs_speed_when_stopped();
    if (adc_speed > max_adc_stop_speed) {                        // 如果车辆速度大于最大停车速度
        AINFO << "ADC not stopped: speed[" << adc_speed << "]";  // 调试信息：车辆未停止
        return false;                                            // 返回false
    }

    // 检查是否足够接近停止线
    const double adc_front_edge_s = reference_line_info.AdcSlBoundary().end_s();              // 获取车辆前沿位置
    const double stop_fence_start_s = frame.open_space_info().open_space_pre_stop_fence_s();  // 获取停止围栏起始位置
    const double distance_stop_line_to_adc_front_edge
            = stop_fence_start_s - adc_front_edge_s;  // 计算停止线到车辆前沿的距离

    if (distance_stop_line_to_adc_front_edge
        > scenario_config_.max_valid_stop_distance()) {        // 如果距离大于最大有效停车距离
        AINFO << "not a valid stop. too far from stop line.";  // 调试信息：距离停止线太远
        return false;                                          // 返回false
    }
    return true;  // 返回true
}

}  // namespace planning
}  // namespace apollo
