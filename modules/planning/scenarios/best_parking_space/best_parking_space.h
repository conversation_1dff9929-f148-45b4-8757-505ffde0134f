#pragma once  // 防止头文件被多次包含
#include <memory>  // 包含智能指针库
#include <string>  // 包含字符串库
#include "modules/common_msgs/map_msgs/map_id.pb.h"  // 包含地图ID的消息定义
#include "modules/planning/scenarios/best_parking_space/proto/best_parking_space.pb.h"  // 包含最佳停车位场景的配置
#include "cyber/plugin_manager/plugin_manager.h"  // 包含插件管理器
#include "modules/map/hdmap/hdmap_util.h"  // 包含高清地图工具
#include "modules/map/pnc_map/path.h"  // 包含PNC地图路径
#include "modules/planning/planning_interface_base/scenario_base/scenario.h"  // 包含场景基类的定义
#include "gtest/gtest.h"  // 包含Google测试框架
#include "cyber/common/file.h"  // 包含文件操作工具
#include "cyber/common/log.h"  // 包含日志工具
#include "modules/planning/planning_base/gflags/planning_gflags.h"  // 包含规划标志
namespace apollo {  // apollo命名空间开始
namespace planning {  // planning命名空间开始

struct BestParkingSpaceContext : public ScenarioContext {  // 定义最佳停车位场景的上下文结构
    BestParkingSpaceConfig scenario_config;  // 场景配置
    std::string target_parking_spot_id;  // 目标停车位ID
    bool pre_stop_rightaway_flag = false;  // 预停车标志
    hdmap::MapPathPoint pre_stop_rightaway_point;  // 预停车点
};

class BestParkingSpaceScenario : public Scenario {  // 定义最佳停车位场景类，继承自场景基类
public:
    bool Init(std::shared_ptr<DependencyInjector> injector, const std::string& name) override;  // 初始化函数

    /**
     * @brief 获取场景上下文
     */
    BestParkingSpaceContext* GetContext() override {  // 获取场景上下文的函数
        return &context_;  // 返回上下文
    }

    bool IsTransferable(const Scenario* const other_scenario, const Frame& frame) override;  // 判断是否可转换场景的函数

private:
    static bool SearchTargetParkingSpotOnPath(  // 静态函数，搜索路径上的目标停车位
            const hdmap::Path& nearby_path,  // 路径
            const std::string& target_parking_id,  // 目标停车位ID
            hdmap::PathOverlap* parking_space_overlap);  // 停车位重叠信息
    static bool CheckDistanceToParkingSpot(  // 静态函数，检查到停车位的距离
            const Frame& frame,  // 框架
            const common::VehicleState& vehicle_state,  // 车辆状态
            const hdmap::Path& nearby_path,  // 路径
            const double parking_start_range,  // 停车开始范围
            const hdmap::PathOverlap& parking_space_overlap);  // 停车位重叠信息

private:
    bool init_ = false;  // 初始化标志
    BestParkingSpaceContext context_;  // 场景上下文
    const hdmap::HDMap* hdmap_ = nullptr;  // 高清地图指针
};

CYBER_PLUGIN_MANAGER_REGISTER_PLUGIN(apollo::planning::BestParkingSpaceScenario, Scenario)  // 注册插件

}  // namespace planning  // 结束planning命名空间
}  // namespace apollo  // 结束apollo命名空间
