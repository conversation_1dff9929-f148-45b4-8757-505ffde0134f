#include "modules/planning/tasks/speed_decider/speed_decider.h"  // 包含速度决策器的头文件

#include <algorithm>  // 包含算法相关的头文件，如 std::sort
#include <memory>     // 包含智能指针相关的头文件，如 std::shared_ptr

#include "modules/common_msgs/perception_msgs/perception_obstacle.pb.h"  // 包含感知障碍物消息的头文件
#include "modules/common_msgs/planning_msgs/decision.pb.h"               // 包含规划决策消息的头文件

#include "cyber/common/log.h"                                        // 包含日志记录相关的头文件
#include "cyber/time/clock.h"                                        // 包含时间相关的头文件
#include "modules/common/configs/vehicle_config_helper.h"            // 包含车辆配置助手相关的头文件
#include "modules/common/util/util.h"                                // 包含通用工具函数相关的头文件
#include "modules/planning/planning_base/common/planning_context.h"  // 包含规划上下文相关的头文件
#include "modules/planning/planning_base/gflags/planning_gflags.h"   // 包含规划模块的 gflags 配置文件
#include "modules/planning/planning_interface_base/task_base/utils/st_gap_estimator.h"  // 包含ST间隙估计器的头文件

namespace apollo {
namespace planning {

using apollo::common::ErrorCode;               // 使用 common 命名空间下的 ErrorCode
using apollo::common::Status;                  // 使用 common 命名空间下的 Status
using apollo::common::VehicleConfigHelper;     // 使用 common 命名空间下的 VehicleConfigHelper
using apollo::common::math::Vec2d;             // 使用 common::math 命名空间下的 Vec2d
using apollo::cyber::Clock;                    // 使用 cyber 命名空间下的 Clock
using apollo::perception::PerceptionObstacle;  // 使用 perception 命名空间下的 PerceptionObstacle

// SpeedDecider 类的初始化函数
bool SpeedDecider::Init(
        const std::string& config_dir,                          // 配置目录路径
        const std::string& name,                                // 任务名称
        const std::shared_ptr<DependencyInjector>& injector) {  // 依赖注入器
    // 调用基类 Task 的 Init 函数
    if (!Task::Init(config_dir, name, injector)) {
        return false;  // 初始化失败则返回 false
    }
    // 加载此任务的配置
    if (!Task::LoadConfig<SpeedDeciderConfig>(&config_)) {
        return false;  // 加载配置失败则返回 false
    }
    // 从配置中加载跟车距离调度器的跟车距离函数
    for (const auto& follow_function : config_.follow_distance_scheduler().follow_distance()) {
        follow_distance_function_.emplace_back(
                std::make_pair(follow_function.speed(), follow_function.slope()));  // 将速度和斜率存入跟车距离函数列表
    }
    // 对跟车距离函数按速度进行排序
    std::sort(follow_distance_function_.begin(), follow_distance_function_.end());
    // 打印排序后的跟车距离函数信息
    for (const auto& iter : follow_distance_function_) {
        AINFO << "speed: " << iter.first << ", slope: " << iter.second;  // 记录日志：速度和对应的斜率
    }
    return true;  // 初始化成功返回 true
}

// SpeedDecider 类的执行函数
common::Status SpeedDecider::Execute(Frame* frame, ReferenceLineInfo* reference_line_info) {
    Task::Execute(frame, reference_line_info);                  // 调用基类 Task 的 Execute 函数
    init_point_ = frame_->PlanningStartPoint();                 // 获取规划起始点
    adc_sl_boundary_ = reference_line_info_->AdcSlBoundary();   // 获取自车在SL坐标系下的边界
    reference_line_ = &reference_line_info_->reference_line();  // 获取参考线
    // 根据速度数据和路径决策生成障碍物决策
    if (!MakeObjectDecision(reference_line_info->speed_data(), reference_line_info->path_decision()).ok()) {
        const std::string msg = "Get object decision by speed profile failed.";  // 错误信息
        AERROR << msg;                                                           // 记录错误日志
        return Status(ErrorCode::PLANNING_ERROR, msg);                           // 返回规划错误状态
    }
    return Status::OK();  // 执行成功返回 OK 状态
}

// 获取障碍物在ST图中的位置
SpeedDecider::STLocation SpeedDecider::GetSTLocation(
        const PathDecision* const path_decision,  // 路径决策
        const SpeedData& speed_profile,           // 速度规划数据
        const STBoundary& st_boundary) const {    // ST边界
    // 如果ST边界为空，则认为在下方
    if (st_boundary.IsEmpty()) {
        return BELOW;
    }

    STLocation st_location = BELOW;              // 默认为BELOW (在下方)
    bool st_position_set = false;                // ST位置是否已设置的标志
    const double start_t = st_boundary.min_t();  // ST边界的最小时间
    const double end_t = st_boundary.max_t();    // ST边界的最大时间
    // 遍历速度规划的每个点
    for (size_t i = 0; i + 1 < speed_profile.size(); ++i) {
        const STPoint curr_st(speed_profile[i].s(), speed_profile[i].t());          // 当前ST点
        const STPoint next_st(speed_profile[i + 1].s(), speed_profile[i + 1].t());  // 下一个ST点
        // 如果当前点和下一点的时间都小于ST边界的最小时间，则继续
        if (curr_st.t() < start_t && next_st.t() < start_t) {
            continue;
        }
        // 如果当前点的时间大于ST边界的最大时间，则跳出循环
        if (curr_st.t() > end_t) {
            break;
        }

        // 如果不使用ST可行驶边界
        if (!FLAGS_use_st_drivable_boundary) {
            common::math::LineSegment2d speed_line(curr_st, next_st);  // 创建速度线段
            // 如果速度线段与ST边界有重叠
            if (st_boundary.HasOverlap(speed_line)) {
                ADEBUG << "speed profile cross st_boundaries.";  // 记录调试日志：速度规划穿过ST边界
                st_location = CROSS;                             // 位置设为CROSS (穿过)

                // 如果不使用ST可行驶边界，且边界类型为保持畅通区
                if (!FLAGS_use_st_drivable_boundary) {
                    if (st_boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
                        // 检查保持畅通区是否可穿越
                        if (!CheckKeepClearCrossable(path_decision, speed_profile, st_boundary)) {
                            st_location = BELOW;  // 如果不可穿越，则位置设为BELOW
                        }
                    }
                }
                break;  // 跳出循环
            }
        }

        // 注意：st_position可以通过一次检查两个st点来计算
        // 但我们需要遍历所有st点以确保没有CROSS
        if (!st_position_set) {
            // 如果ST边界的时间范围与当前速度点和下个速度点的时间范围有交集
            if (start_t < next_st.t() && curr_st.t() < end_t) {
                STPoint bd_point_front = st_boundary.upper_points().front();  // 获取ST边界上边界的前点
                // 计算叉乘来判断相对位置
                double side = common::math::CrossProd(bd_point_front, curr_st, next_st);
                st_location = side < 0.0 ? ABOVE : BELOW;  // side < 0.0 表示在上方，否则在下方
                st_position_set = true;                    // ST位置已设置
            }
        }
    }
    return st_location;  // 返回ST位置
}

// 检查保持畅通区(Keep Clear Zone)是否可穿越
bool SpeedDecider::CheckKeepClearCrossable(
        const PathDecision* const path_decision,           // 路径决策
        const SpeedData& speed_profile,                    // 速度规划数据
        const STBoundary& keep_clear_st_boundary) const {  // 保持畅通区的ST边界
    bool keep_clear_crossable = true;                      // 默认为可穿越

    const auto& last_speed_point = speed_profile.back();  // 获取速度规划的最后一个点
    double last_speed_point_v = 0.0;                      // 最后一个速度点的速度
    // 如果最后一个速度点有速度值
    if (last_speed_point.has_v()) {
        last_speed_point_v = last_speed_point.v();
    } else {
        // 如果没有速度值，则通过最后两个点计算速度
        const size_t len = speed_profile.size();
        if (len > 1) {
            const auto& last_2nd_speed_point = speed_profile[len - 2];
            last_speed_point_v = (last_speed_point.s() - last_2nd_speed_point.s())
                    / (last_speed_point.t() - last_2nd_speed_point.t());
        }
    }
    ADEBUG << "last_speed_point_s[" << last_speed_point.s() << "] st_boundary.max_s[" << keep_clear_st_boundary.max_s()
           << "] last_speed_point_v[" << last_speed_point_v << "]";  // 记录调试日志
    // 如果最后一个速度点的s值小于等于保持畅通区ST边界的最大s值，且速度小于配置的最小速度阈值
    if (last_speed_point.s() <= keep_clear_st_boundary.max_s()
        && last_speed_point_v < config_.keep_clear_last_point_speed()) {
        keep_clear_crossable = false;  // 则认为不可穿越
    }
    return keep_clear_crossable;  // 返回是否可穿越
}

// 检查保持畅通区(Keep Clear Zone)是否被阻塞
bool SpeedDecider::CheckKeepClearBlocked(const PathDecision* const path_decision, const Obstacle& keep_clear_obstacle)
        const {
    bool keep_clear_blocked = false;  // 默认为未被阻塞

    // 检查是否与其他停止墙重叠
    for (const auto* obstacle : path_decision->obstacles().Items()) {
        // 跳过自身
        if (obstacle->Id() == keep_clear_obstacle.Id()) {
            continue;
        }
        const double obstacle_start_s = obstacle->PerceptionSLBoundary().start_s();           // 其他障碍物的起始s值
        const double adc_length = VehicleConfigHelper::GetConfig().vehicle_param().length();  // 自车长度
        const double distance
                = obstacle_start_s - keep_clear_obstacle.PerceptionSLBoundary().end_s();  // 与其他障碍物的距离

        // 如果其他障碍物是阻塞性障碍物，且距离在0到半个车长之间
        if (obstacle->IsBlockingObstacle() && distance > 0 && distance < (adc_length / 2)) {
            keep_clear_blocked = true;  // 则认为保持畅通区被阻塞
            break;                      // 跳出循环
        }
    }
    return keep_clear_blocked;  // 返回是否被阻塞
}

// 检查是否跟车过近
bool SpeedDecider::IsFollowTooClose(const Obstacle& obstacle) const {
    // 如果障碍物不是阻塞性障碍物，则不认为跟车过近
    if (!obstacle.IsBlockingObstacle()) {
        return false;
    }

    // 如果障碍物路径ST边界的最小时间大于0，说明障碍物在未来出现，不认为跟车过近
    if (obstacle.path_st_boundary().min_t() > 0.0) {
        return false;
    }
    const double obs_speed = obstacle.speed();  // 障碍物速度
    const double ego_speed = init_point_.v();   // 自车速度
    // 如果障碍物速度大于自车速度，不认为跟车过近
    if (obs_speed > ego_speed) {
        return false;
    }
    // 计算与障碍物的距离，减去最小停车距离
    const double distance = obstacle.path_st_boundary().min_s() - FLAGS_min_stop_distance_obstacle;
    static constexpr double lane_follow_max_decel = 3.0;  // 车道保持时的最大减速度
    static constexpr double lane_change_max_decel = 3.0;  // 换道时的最大减速度
    auto* planning_status
            = injector_->planning_context()->mutable_planning_status()->mutable_change_lane();  // 获取换道状态
    double distance_numerator = std::pow((ego_speed - obs_speed), 2)
            * 0.5;  // 距离计算公式的分子部分 (v_ego^2 - v_obs^2) / 2a 中的 (v_ego - v_obs)^2 * 0.5
    double distance_denominator = lane_follow_max_decel;  // 距离计算公式的分母部分 (减速度 a)
    // 如果正在换道，使用换道时的最大减速度
    if (planning_status->has_status() && planning_status->status() == ChangeLaneStatus::IN_CHANGE_LANE) {
        distance_denominator = lane_change_max_decel;
    }
    // 如果实际距离小于计算出的安全距离，则认为跟车过近
    return distance < distance_numerator / distance_denominator;
}

// 生成障碍物决策
Status SpeedDecider::MakeObjectDecision(const SpeedData& speed_profile, PathDecision* const path_decision) const {
    // 如果速度规划的点数小于2，无法进行决策
    if (speed_profile.size() < 2) {
        const std::string msg = "dp_st_graph failed to get speed profile.";  // 错误信息
        AERROR << msg;                                                       // 记录错误日志
        return Status(ErrorCode::PLANNING_ERROR, msg);                       // 返回规划错误状态
    }

    // 遍历路径决策中的所有障碍物
    for (const auto* obstacle : path_decision->obstacles().Items()) {
        auto* mutable_obstacle = path_decision->Find(obstacle->Id());  // 获取可修改的障碍物对象
        const auto& boundary = mutable_obstacle->path_st_boundary();   // 获取障碍物的路径ST边界

        // 如果ST边界为空，或最大s/t为负，或最小t大于速度规划的最后时间点，则忽略此障碍物
        if (boundary.IsEmpty() || boundary.max_s() < 0.0 || boundary.max_t() < 0.0
            || boundary.min_t() >= speed_profile.back().t()) {
            AppendIgnoreDecision(mutable_obstacle);  // 添加忽略决策
            continue;                                // 继续下一个障碍物
        }
        // 如果障碍物已经有纵向决策，则忽略
        if (obstacle->HasLongitudinalDecision()) {
            AppendIgnoreDecision(mutable_obstacle);  // 添加忽略决策
            continue;                                // 继续下一个障碍物
        }

        // 对于虚拟障碍物，如果中心点不在车道上，则跳过
        if (obstacle->IsVirtual()) {
            const auto& obstacle_box = obstacle->PerceptionBoundingBox();  // 获取障碍物的感知包围盒
            if (!reference_line_->IsOnLane(obstacle_box.center())) {       // 判断中心点是否在车道上
                continue;                                                  // 继续下一个障碍物
            }
        }

        // 如果配置了停车避让行人，并且检查需要为行人停车
        if (config_.is_stop_for_pedestrain() && CheckStopForPedestrian(*mutable_obstacle)) {
            ObjectDecisionType stop_decision;  // 创建停车决策
            // 创建停车决策，停车距离为负的最小停车距离障碍物
            if (CreateStopDecision(*mutable_obstacle, &stop_decision, -FLAGS_min_stop_distance_obstacle)) {
                mutable_obstacle->AddLongitudinalDecision(
                        "dp_st_graph/pedestrian", stop_decision);  // 添加纵向决策：为行人停车
            }
            continue;  // 继续下一个障碍物
        }

        // 获取障碍物在ST图中的位置
        auto location = GetSTLocation(path_decision, speed_profile, boundary);

        // 如果不使用ST可行驶边界
        if (!FLAGS_use_st_drivable_boundary) {
            // 如果边界类型为保持畅通区
            if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
                // 如果保持畅通区被阻塞，则位置设为BELOW
                if (CheckKeepClearBlocked(path_decision, *obstacle)) {
                    location = BELOW;
                }
            }
        }

        // 根据ST位置进行决策
        switch (location) {
        case BELOW:                                                                  // 在下方
            if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {  // 如果是保持畅通区
                ObjectDecisionType stop_decision;                                    // 创建停车决策
                // 创建停车决策，停车距离为0
                if (CreateStopDecision(*mutable_obstacle, &stop_decision, 0.0)) {
                    mutable_obstacle->AddLongitudinalDecision(
                            "dp_st_graph/keep_clear", stop_decision);  // 添加纵向决策：为保持畅通区停车
                }
            } else if (obstacle->IsStatic()) {  // 如果是静态障碍物
                // 为静态障碍物停车
                ObjectDecisionType stop_decision;  // 创建停车决策
                // 创建停车决策，停车距离为负的最小停车距离障碍物
                if (CreateStopDecision(*mutable_obstacle, &stop_decision, -FLAGS_min_stop_distance_obstacle)) {
                    mutable_obstacle->AddLongitudinalDecision("dp_st_graph", stop_decision);  // 添加纵向决策：停车
                }
            } else if (CheckIsFollow(*obstacle, boundary)) {  // 如果是跟车情况
                // 如果跟车过近，则停车
                if (IsFollowTooClose(*mutable_obstacle)) {
                    ObjectDecisionType stop_decision;  // 创建停车决策
                    // 创建停车决策，停车距离为负的最小停车距离障碍物
                    if (CreateStopDecision(*mutable_obstacle, &stop_decision, -FLAGS_min_stop_distance_obstacle)) {
                        mutable_obstacle->AddLongitudinalDecision(
                                "dp_st_graph/too_close", stop_decision);  // 添加纵向决策：因过近而停车
                    }
                } else {  // 高速或低速加速情况
                    // 跟车决策
                    ObjectDecisionType follow_decision;  // 创建跟车决策
                    if (CreateFollowDecision(*mutable_obstacle, &follow_decision)) {
                        mutable_obstacle->AddLongitudinalDecision(
                                "dp_st_graph", follow_decision);  // 添加纵向决策：跟车
                    }
                }
            } else {  // 其他情况，如汇入等
                // 减速让行决策
                ObjectDecisionType yield_decision;  // 创建减速让行决策
                if (CreateYieldDecision(*mutable_obstacle, &yield_decision)) {
                    mutable_obstacle->AddLongitudinalDecision("dp_st_graph", yield_decision);  // 添加纵向决策：减速让行
                }
            }
            break;
        case ABOVE:                                                                  // 在上方
            if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {  // 如果是保持畅通区
                ObjectDecisionType ignore;                                           // 创建忽略决策
                ignore.mutable_ignore();
                mutable_obstacle->AddLongitudinalDecision("dp_st_graph", ignore);  // 添加纵向决策：忽略
            } else {
                // 超车决策
                ObjectDecisionType overtake_decision;  // 创建超车决策
                if (CreateOvertakeDecision(*mutable_obstacle, &overtake_decision)) {
                    mutable_obstacle->AddLongitudinalDecision(
                            "dp_st_graph/overtake", overtake_decision);  // 添加纵向决策：超车
                }
            }
            break;
        case CROSS:                                        // 穿过
            if (mutable_obstacle->IsBlockingObstacle()) {  // 如果是阻塞性障碍物
                ObjectDecisionType stop_decision;          // 创建停车决策
                // 创建停车决策，停车距离为负的最小停车距离障碍物
                if (CreateStopDecision(*mutable_obstacle, &stop_decision, -FLAGS_min_stop_distance_obstacle)) {
                    mutable_obstacle->AddLongitudinalDecision(
                            "dp_st_graph/cross", stop_decision);  // 添加纵向决策：因穿过而停车
                }
                const std::string msg = absl::StrCat(
                        "Failed to find a solution for crossing obstacle: ", mutable_obstacle->Id());  // 错误信息
                AERROR << msg;                                                                         // 记录错误日志
                return Status(ErrorCode::PLANNING_ERROR, msg);  // 返回规划错误状态
            }
            break;
        default:                                        // 未知位置
            AERROR << "Unknown position:" << location;  // 记录错误日志
        }
        AppendIgnoreDecision(mutable_obstacle);  // 为没有决策的障碍物添加忽略决策
    }

    return Status::OK();  // 执行成功返回 OK 状态
}

// 为障碍物添加忽略决策
void SpeedDecider::AppendIgnoreDecision(Obstacle* obstacle) const {
    ObjectDecisionType ignore_decision;  // 创建忽略决策
    ignore_decision.mutable_ignore();
    // 如果障碍物没有纵向决策，则添加忽略的纵向决策
    if (!obstacle->HasLongitudinalDecision()) {
        obstacle->AddLongitudinalDecision("dp_st_graph", ignore_decision);
    }
    // 如果障碍物没有横向决策，则添加忽略的横向决策
    if (!obstacle->HasLateralDecision()) {
        obstacle->AddLateralDecision("dp_st_graph", ignore_decision);
    }
}

// 创建停车决策
bool SpeedDecider::CreateStopDecision(
        const Obstacle& obstacle,                        // 障碍物对象
        ObjectDecisionType* const stop_decision,         // 停车决策输出参数
        double stop_distance) const {                    // 停车距离
    const auto& boundary = obstacle.path_st_boundary();  // 获取障碍物的路径ST边界

    // TODO(all): 这是一个bug！不能混合参考s和路径s！
    // 用计算出的参考线s替换boundary.min_s()
    // 停车点是根据参考线s设置的。
    double fence_s = adc_sl_boundary_.end_s() + boundary.min_s() + stop_distance;  // 计算停车点在参考线上的s值
    // 如果是保持畅通区，停车点s值为其感知SL边界的起始s
    if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
        fence_s = obstacle.PerceptionSLBoundary().start_s();
    }
    // 获取主停车决策的参考线s值
    const double main_stop_s = reference_line_info_->path_decision()->stop_reference_line_s();
    // 如果主停车点比当前计算的停车点更近，则忽略此停车决策
    if (main_stop_s < fence_s) {
        ADEBUG << "Stop fence is further away, ignore.";  // 记录调试日志
        return false;                                     // 返回false，表示未创建停车决策
    }

    const auto fence_point = reference_line_->GetReferencePoint(fence_s);  // 获取参考线上停车点的坐标

    // 设置停车决策
    auto* stop = stop_decision->mutable_stop();     // 获取可修改的stop对象
    stop->set_distance_s(stop_distance);            // 设置停车距离
    auto* stop_point = stop->mutable_stop_point();  // 获取可修改的stop_point对象
    stop_point->set_x(fence_point.x());             // 设置停车点x坐标
    stop_point->set_y(fence_point.y());             // 设置停车点y坐标
    stop_point->set_z(0.0);                         // 设置停车点z坐标 (通常为0)
    stop->set_stop_heading(fence_point.heading());  // 设置停车朝向

    // 如果是保持畅通区，设置停车原因为清空区域
    if (boundary.boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
        stop->set_reason_code(StopReasonCode::STOP_REASON_CLEAR_ZONE);
    }

    PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();  // 获取障碍物感知类型
    ADEBUG << "STOP: obstacle_id[" << obstacle.Id() << "] obstacle_type[" << PerceptionObstacle_Type_Name(obstacle_type)
           << "]";  // 记录调试日志：停车决策信息

    return true;  // 创建停车决策成功
}

// 创建跟车决策
bool SpeedDecider::CreateFollowDecision(const Obstacle& obstacle, ObjectDecisionType* const follow_decision) const {
    const double follow_speed = init_point_.v();  // 获取当前自车速度作为跟车速度参考
    const double follow_distance_s
            = -EstimateProperFollowGap(follow_speed);  // 估算合适的跟车距离 (负值表示在障碍物后方)

    const auto& boundary = obstacle.path_st_boundary();  // 获取障碍物的路径ST边界
    // 计算跟车点在参考线上的s值
    const double reference_s = adc_sl_boundary_.end_s() + boundary.min_s() + follow_distance_s;
    // 获取主停车决策的参考线s值
    const double main_stop_s = reference_line_info_->path_decision()->stop_reference_line_s();
    // 如果主停车点比当前计算的跟车点更近，则忽略此跟车决策
    if (main_stop_s < reference_s) {
        ADEBUG << "Follow reference_s is further away, ignore.";  // 记录调试日志
        return false;                                             // 返回false，表示未创建跟车决策
    }

    auto ref_point = reference_line_->GetReferencePoint(reference_s);  // 获取参考线上跟车点的坐标

    // 设置跟车决策
    auto* follow = follow_decision->mutable_follow();   // 获取可修改的follow对象
    follow->set_distance_s(follow_distance_s);          // 设置跟车距离
    auto* fence_point = follow->mutable_fence_point();  // 获取可修改的fence_point对象 (跟车目标点)
    fence_point->set_x(ref_point.x());                  // 设置跟车目标点x坐标
    fence_point->set_y(ref_point.y());                  // 设置跟车目标点y坐标
    fence_point->set_z(0.0);                            // 设置跟车目标点z坐标 (通常为0)
    follow->set_fence_heading(ref_point.heading());     // 设置跟车目标点朝向

    PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();  // 获取障碍物感知类型
    ADEBUG << "FOLLOW: obstacle_id[" << obstacle.Id() << "] obstacle_type["
           << PerceptionObstacle_Type_Name(obstacle_type) << "]";  // 记录调试日志：跟车决策信息

    return true;  // 创建跟车决策成功
}

// 创建减速让行决策
bool SpeedDecider::CreateYieldDecision(const Obstacle& obstacle, ObjectDecisionType* const yield_decision) const {
    PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();  // 获取障碍物感知类型
    double yield_distance = config_.yield_distance_buffer();                // 获取配置的让行距离缓冲

    const auto& obstacle_boundary = obstacle.path_st_boundary();  // 获取障碍物的路径ST边界
    // 计算让行距离，取障碍物路径ST边界最小s值的负数和配置的让行缓冲距离的负数中的较大值 (更靠近障碍物)
    const double yield_distance_s = std::max(-obstacle_boundary.min_s(), -yield_distance);

    // 计算让行点在参考线上的s值
    const double reference_line_fence_s = adc_sl_boundary_.end_s() + obstacle_boundary.min_s() + yield_distance_s;
    // 获取主停车决策的参考线s值
    const double main_stop_s = reference_line_info_->path_decision()->stop_reference_line_s();
    // 如果主停车点比当前计算的让行点更近，则忽略此让行决策
    if (main_stop_s < reference_line_fence_s) {
        ADEBUG << "Yield reference_s is further away, ignore.";  // 记录调试日志
        return false;                                            // 返回false，表示未创建让行决策
    }

    auto ref_point = reference_line_->GetReferencePoint(reference_line_fence_s);  // 获取参考线上让行点的坐标

    // 设置减速让行决策
    auto* yield = yield_decision->mutable_yield();       // 获取可修改的yield对象
    yield->set_distance_s(yield_distance_s);             // 设置让行距离
    yield->mutable_fence_point()->set_x(ref_point.x());  // 设置让行目标点x坐标
    yield->mutable_fence_point()->set_y(ref_point.y());  // 设置让行目标点y坐标
    yield->mutable_fence_point()->set_z(0.0);            // 设置让行目标点z坐标 (通常为0)
    yield->set_fence_heading(ref_point.heading());       // 设置让行目标点朝向

    ADEBUG << "YIELD: obstacle_id[" << obstacle.Id() << "] obstacle_type["
           << PerceptionObstacle_Type_Name(obstacle_type) << "]";  // 记录调试日志：让行决策信息

    return true;  // 创建让行决策成功
}

// 创建超车决策
bool SpeedDecider::CreateOvertakeDecision(const Obstacle& obstacle, ObjectDecisionType* const overtake_decision) const {
    const auto& velocity = obstacle.Perception().velocity();  // 获取障碍物的感知速度
    // 计算障碍物速度在自车路径方向上的投影
    const double obstacle_speed = common::math::Vec2d::CreateUnitVec2d(init_point_.path_point().theta())
                                          .InnerProd(Vec2d(velocity.x(), velocity.y()));

    // 估算合适的超车距离
    double overtake_distance_s = EstimateProperOvertakingGap(obstacle_speed, init_point_.v());

    const auto& boundary = obstacle.path_st_boundary();  // 获取障碍物的路径ST边界
    // 计算超车点在参考线上的s值
    const double reference_line_fence_s = adc_sl_boundary_.end_s() + boundary.min_s() + overtake_distance_s;
    // 获取主停车决策的参考线s值
    const double main_stop_s = reference_line_info_->path_decision()->stop_reference_line_s();
    // 如果主停车点比当前计算的超车点更近（意味着在超车完成前就需要停车），则忽略此超车决策
    if (main_stop_s < reference_line_fence_s) {
        ADEBUG << "Overtake reference_s is further away, ignore.";  // 记录调试日志
        return false;                                               // 返回false，表示未创建超车决策
    }

    auto ref_point = reference_line_->GetReferencePoint(reference_line_fence_s);  // 获取参考线上超车点的坐标

    // 设置超车决策
    auto* overtake = overtake_decision->mutable_overtake();  // 获取可修改的overtake对象
    overtake->set_distance_s(overtake_distance_s);           // 设置超车距离
    overtake->mutable_fence_point()->set_x(ref_point.x());   // 设置超车目标点x坐标
    overtake->mutable_fence_point()->set_y(ref_point.y());   // 设置超车目标点y坐标
    overtake->mutable_fence_point()->set_z(0.0);             // 设置超车目标点z坐标 (通常为0)
    overtake->set_fence_heading(ref_point.heading());        // 设置超车目标点朝向

    PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();  // 获取障碍物感知类型
    ADEBUG << "OVERTAKE: obstacle_id[" << obstacle.Id() << "] obstacle_type["
           << PerceptionObstacle_Type_Name(obstacle_type) << "]";  // 记录调试日志：超车决策信息

    return true;  // 创建超车决策成功
}

// 检查是否为跟车情况
bool SpeedDecider::CheckIsFollow(const Obstacle& obstacle, const STBoundary& boundary) const {
    // 计算障碍物在SL坐标系下的最小横向距离
    const double obstacle_l_distance = std::min(
            std::fabs(obstacle.PerceptionSLBoundary().start_l()), std::fabs(obstacle.PerceptionSLBoundary().end_l()));
    // 如果障碍物的最小横向距离大于配置的最小横向跟车距离，则不认为是跟车
    if (obstacle_l_distance > config_.follow_min_obs_lateral_distance()) {
        return false;
    }

    // 如果障碍物ST边界的左下角s值大于右下角s值，说明障碍物正在远离自车，不认为是跟车
    // (通常 bottom_left_point.s < bottom_right_point.s 表示障碍物朝向自车或与自车同向)
    if (boundary.bottom_left_point().s() > boundary.bottom_right_point().s()) {
        return false;
    }

    static constexpr double kFollowTimeEpsilon = 1e-3;  // 跟车时间判断的epsilon值
    static constexpr double kFollowCutOffTime = 0.5;    // 跟车判断的截止时间
    // 如果障碍物ST边界的最小时间大于截止时间，或者最大时间小于epsilon，则不认为是跟车
    if (boundary.min_t() > kFollowCutOffTime || boundary.max_t() < kFollowTimeEpsilon) {
        return false;
    }

    // 如果障碍物ST边界的持续时间小于配置的最小跟车时间，则不认为是跟车
    if (boundary.max_t() - boundary.min_t() < config_.follow_min_time_sec()) {
        return false;
    }

    return true;  // 满足以上条件，则认为是跟车情况
}

// 检查是否需要为行人停车
bool SpeedDecider::CheckStopForPedestrian(const Obstacle& obstacle) const {
    const auto& perception_obstacle = obstacle.Perception();  // 获取感知障碍物信息
    // 如果障碍物类型不是行人，则不需要为行人停车
    if (perception_obstacle.type() != PerceptionObstacle::PEDESTRIAN) {
        return false;
    }

    const auto& obstacle_sl_boundary = obstacle.PerceptionSLBoundary();  // 获取障碍物的感知SL边界
    // 如果行人的SL边界的末端s值小于自车SL边界的起始s值 (即行人在自车后方)，则不需要停车
    if (obstacle_sl_boundary.end_s() < adc_sl_boundary_.start_s()) {
        return false;
    }

    // 从PlanningContext读取行人停车时间信息
    auto* mutable_speed_decider_status
            = injector_->planning_context()->mutable_planning_status()->mutable_speed_decider();
    std::unordered_map<std::string, double> stop_time_map;  // 用于存储行人ID和停车时间戳的映射
    // 将已有的行人停车时间信息加载到map中
    for (const auto& pedestrian_stop_time : mutable_speed_decider_status->pedestrian_stop_time()) {
        stop_time_map[pedestrian_stop_time.obstacle_id()] = pedestrian_stop_time.stop_timestamp_sec();
    }

    const std::string& obstacle_id = obstacle.Id();  // 获取当前行人障碍物的ID

    // 更新静态行人的停车时间戳以用于计时器
    // 检查静态行人的停车计时器
    static constexpr double kSDistanceStartTimer = 10.0;   // 开始计时器的S距离阈值 (行人与自车距离小于此值时开始计时)
    static constexpr double kMaxStopSpeed = 0.3;           // 判断行人是否静止的最大速度阈值 (m/s)
    static constexpr double kPedestrianStopTimeout = 4.0;  // 行人停车超时时间 (秒)

    bool result = true;  // 默认为需要停车
    // 如果行人与自车的路径ST边界最小s值小于开始计时器的距离阈值
    if (obstacle.path_st_boundary().min_s() < kSDistanceStartTimer) {
        const auto obstacle_speed = std::hypot(
                perception_obstacle.velocity().x(), perception_obstacle.velocity().y());  // 计算行人速度大小
        // 如果行人速度大于最大静止速度阈值 (即行人正在移动)
        if (obstacle_speed > kMaxStopSpeed) {
            stop_time_map.erase(obstacle_id);  // 从map中移除此行人的停车时间戳 (因为行人已移动)
        } else {                               // 行人静止或速度很慢
            // 如果map中没有此行人的停车时间戳
            if (stop_time_map.count(obstacle_id) == 0) {
                // 添加当前时间戳
                stop_time_map[obstacle_id] = Clock::NowInSeconds();
                ADEBUG << "add timestamp: obstacle_id[" << obstacle_id << "] timestamp[" << Clock::NowInSeconds()
                       << "]";  // 记录调试日志
            } else {
                // 如果已有时间戳，则检查是否超时
                double stop_timer = Clock::NowInSeconds() - stop_time_map[obstacle_id];  // 计算已停车时间
                ADEBUG << "stop_timer: obstacle_id[" << obstacle_id << "] stop_timer[" << stop_timer
                       << "]";  // 记录调试日志
                // 如果已停车时间大于等于超时时间
                if (stop_timer >= kPedestrianStopTimeout) {
                    result = false;  // 则不需要再为该行人停车 (认为行人会一直静止，可以绕行或忽略)
                }
            }
        }
    }

    // 将更新后的行人停车时间信息写回PlanningContext
    mutable_speed_decider_status->mutable_pedestrian_stop_time()->Clear();  // 清空旧的停车时间信息
    // 添加新的停车时间信息
    for (const auto& stop_time : stop_time_map) {
        auto pedestrian_stop_time = mutable_speed_decider_status->add_pedestrian_stop_time();
        pedestrian_stop_time->set_obstacle_id(stop_time.first);
        pedestrian_stop_time->set_stop_timestamp_sec(stop_time.second);
    }
    return result;  // 返回是否需要为行人停车
}

// 估算合适的超车间隙
double SpeedDecider::EstimateProperOvertakingGap(const double target_obs_speed, const double adc_speed) const {
    // 超车距离取 (max(自车速度, 目标障碍物速度) * 超车时间缓冲) 和 (最小超车距离) 中的较大值
    const double overtake_distance_s = std::fmax(
            std::fmax(adc_speed, target_obs_speed) * config_.overtake_time_buffer(), config_.overtake_min_distance());
    return overtake_distance_s;
}

// 估算合适的跟车间隙
double SpeedDecider::EstimateProperFollowGap(const double& adc_speed) const {
    double follow_distance = config_.stop_follow_distance();  // 基础跟车距离 (停车时的跟车距离)
    // 根据当前自车速度，通过分段线性函数计算额外的跟车距离
    // follow_distance_function_ 存储了一系列 (速度, 斜率) 对，表示在不同速度区间的跟车距离增长率
    for (int i = 1; i < follow_distance_function_.size(); i++) {
        // 如果当前速度小于等于第i个速度点
        if (adc_speed <= follow_distance_function_[i].first) {
            // 在第i-1个速度点和第i个速度点之间进行线性插值
            follow_distance
                    += follow_distance_function_[i - 1].second * (adc_speed - follow_distance_function_[i - 1].first);
            break;  // 计算完毕，跳出循环
        } else {
            // 如果当前速度大于第i个速度点，则累加第i-1到第i速度区间的完整跟车距离增量
            follow_distance += follow_distance_function_[i - 1].second
                    * (follow_distance_function_[i].first - follow_distance_function_[i - 1].first);
        }
    }
    // 如果当前速度大于最后一个速度点
    if (adc_speed > follow_distance_function_.back().first) {
        // 使用最后一个区间的斜率计算超出部分的跟车距离增量
        follow_distance
                += follow_distance_function_.back().second * (adc_speed - follow_distance_function_.back().first);
    }
    AINFO << "follow_distance: " << follow_distance;  // 记录日志：计算出的跟车距离
    return follow_distance;                           // 返回总的跟车距离
}

}  // namespace planning
}  // namespace apollo
