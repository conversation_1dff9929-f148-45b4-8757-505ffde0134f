#include "modules/planning/tasks/path_decider/path_decider.h"  // 规划任务模块中路径决策器的头文件

#include <algorithm>  // C++标准算法库
#include <memory>     // C++智能指针库

#include "modules/common_msgs/planning_msgs/decision.pb.h"  // Apollo规划决策相关的protobuf定义

#include "modules/common/configs/vehicle_config_helper.h"                 // Apollo车辆配置读取辅助类
#include "modules/common/util/util.h"                                     // Apollo通用工具类
#include "modules/planning/planning_base/common/planning_context.h"       // Apollo规划上下文信息
#include "modules/planning/planning_base/common/util/print_debug_info.h"  // Apollo打印调试信息的工具
#include "modules/planning/planning_base/gflags/planning_gflags.h"        // Apollo规划模块的全局配置参数

namespace apollo {
namespace planning {

using apollo::common::ErrorCode;            // 使用Apollo通用错误码
using apollo::common::Status;               // 使用Apollo通用状态类
using apollo::common::VehicleConfigHelper;  // 使用Apollo车辆配置辅助类
using apollo::perception::PerceptionObstacle;
// 初始化 PathDecider 任务
bool PathDecider::Init(
        const std::string &config_dir,
        const std::string &name,
        const std::shared_ptr<DependencyInjector> &injector) {
    // 调用基类 Task 的初始化方法
    if (!Task::Init(config_dir, name, injector)) {
        return false;  // 若基类初始化失败，则返回 false
    }
    // 加载此任务相关的配置 (PathDeciderConfig)
    return Task::LoadConfig<PathDeciderConfig>(&config_);
}

// PathDecider 任务的执行入口
Status PathDecider::Execute(Frame *frame, ReferenceLineInfo *reference_line_info) {
    // 调用基类 Task 的 Execute 方法 (通常为空或执行通用前置操作)
    Task::Execute(frame, reference_line_info);
    // 调用核心处理函数 Process
    return Process(reference_line_info, reference_line_info->path_data(), reference_line_info->path_decision());
}

// PathDecider 的核心处理逻辑
Status PathDecider::Process(
        const ReferenceLineInfo *reference_line_info,
        const PathData &path_data,
        PathDecision *const path_decision) {
    // 如果启用了“跳过路径任务”标志，并且当前参考线信息指示路径可重用，则直接返回OK
    if (FLAGS_enable_skip_path_tasks && reference_line_info->path_reusable()) {
        return Status::OK();
    }

    PrintCurves debug_info;                           // 创建用于打印调试曲线的对象
    const auto &path = path_data.discretized_path();  // 获取离散化后的路径点序列

    // 如果路径不为空，获取路径起点处车辆的包围盒并添加到调试信息中
    if (!path.empty()) {
        const auto &vehicle_box
                = common::VehicleConfigHelper::Instance()->GetBoundingBox(path[0]);  // path[0] 代表路径规划的起始点状态
        debug_info.AddPoint("start_point_box", vehicle_box.GetAllCorners());         // 添加车辆包围盒的角点
    }

    // 将输出路径的每个点添加到调试信息中
    for (const auto &path_pt : path) {
        debug_info.AddPoint("output_path", path_pt.x(), path_pt.y());
    }
    debug_info.PrintToLog();  // 将收集到的调试曲线信息打印到日志

    std::string blocking_obstacle_id;  // 用于存储阻塞车辆行驶的障碍物ID
    // 获取规划上下文中路径决策器状态的可变指针
    auto *mutable_path_decider_status
            = injector_->planning_context()->mutable_planning_status()->mutable_path_decider();
    // // 补充动态障碍物
    // for (auto *obstacle : path_decision->obstacles().Items()) {                         // 遍历所有障碍物
    //     const auto &id = obstacle->Id();                                                // 获取障碍物的ID
    //     const PerceptionObstacle& perception_obstacle = obstacle->Perception();
    //     PerceptionObstacle::Type obstacle_type = perception_obstacle.type();
    //     if (obstacle->speed() < 3 && obstacle_type == 5 && id== "1000_0"){
    //         AINFO << "调整前blocking判断状态为" << obstacle->IsBlockingObstacle() ;
    //         AINFO << "补充逻辑" ;
    //         reference_line_info->SetBlockingObstacle(id) ;
    //         AINFO << "障碍物调整后blocking判断状态为" << obstacle->IsBlockingObstacle() ;
    //     }
    // }

    // 判断是否存在阻塞障碍物
    if (reference_line_info->GetBlockingObstacle() != nullptr) {
        blocking_obstacle_id = reference_line_info->GetBlockingObstacle()->Id();  // 获取阻塞障碍物的ID
        // 获取当前前方静态障碍物出现的周期计数
        int front_static_obstacle_cycle_counter = mutable_path_decider_status->front_static_obstacle_cycle_counter();
        // 更新计数器：如果之前为负（表示 مدتی است مانع استاتیک وجود ندارد），则重置为0
        mutable_path_decider_status->set_front_static_obstacle_cycle_counter(
                std::max(front_static_obstacle_cycle_counter, 0));
        // 更新计数器：递增1，但最大不超过10 (防止无限增长)
        mutable_path_decider_status->set_front_static_obstacle_cycle_counter(
                std::min(front_static_obstacle_cycle_counter + 1, 10));
        // 设置当前帧遇到的前方静态阻塞障碍物的ID
        mutable_path_decider_status->set_front_static_obstacle_id(reference_line_info->GetBlockingObstacle()->Id());
    } else {  // 如果没有阻塞障碍物
        // 获取当前前方静态障碍物出现的周期计数
        int front_static_obstacle_cycle_counter = mutable_path_decider_status->front_static_obstacle_cycle_counter();
        // 更新计数器：如果之前为正（表示之前有障碍物），则重置为0
        mutable_path_decider_status->set_front_static_obstacle_cycle_counter(
                std::min(front_static_obstacle_cycle_counter, 0));
        // 更新计数器：递减1，但最小不低于-10 (表示 مدتی است مانع استاتیک وجود ندارد)
        mutable_path_decider_status->set_front_static_obstacle_cycle_counter(
                std::max(front_static_obstacle_cycle_counter - 1, -10));
        // 如果计数器小于-2 (表示连续多帧没有静态障碍物)，则清空存储的障碍物ID
        if (mutable_path_decider_status->front_static_obstacle_cycle_counter() < -2) {
            std::string id = " ";  // 使用空字符串或特定标记表示无障碍物ID
            mutable_path_decider_status->set_front_static_obstacle_id(id);
        }
    }

    // 基于路径数据和阻塞障碍物ID，为路径上的对象（障碍物）做出决策
    if (!MakeObjectDecision(path_data, blocking_obstacle_id, path_decision)) {
        const std::string msg = "Failed to make decision based on tunnel";  // 错误信息
        AERROR << msg;                                                      // 记录错误日志
        return Status(ErrorCode::PLANNING_ERROR, msg);                      // 返回规划错误状态
    }
    return Status::OK();  // 处理成功
}

// 为路径上的对象（主要是障碍物）做出决策
bool PathDecider::MakeObjectDecision(
        const PathData &path_data,
        const std::string &blocking_obstacle_id,
        PathDecision *const path_decision) {
    // 首先为静态障碍物做出决策
    if (!MakeStaticObstacleDecision(path_data, blocking_obstacle_id, path_decision)) {
        AERROR << "Failed to make decisions for static obstacles";  // 记录错误：为静态障碍物决策失败
        return false;
    }
    // 如果配置中启用了忽略后方障碍物的选项
    if (config_.ignore_backward_obstacle()) {
        IgnoreBackwardObstacle(path_decision);  // 调用函数处理后方障碍物的忽略逻辑
    }

    return true;  // 对象决策成功
}

// 为静态障碍物做出决策 (此函数逻辑较为复杂，涉及多种情况判断)
// TODO(jiacheng): 最终整个 "path_decider" 应该会被废弃。
// 在它被废弃之前，对其逻辑进行了轻微调整，以确保目前一切仍能正常工作。
bool PathDecider::MakeStaticObstacleDecision(
        const PathData &path_data,
        const std::string &blocking_obstacle_id,
        PathDecision *const path_decision) {
    // 前置检查与重要值获取
    ACHECK(path_decision);                                    // 断言 path_decision 指针不为空
    const auto &frenet_path = path_data.frenet_frame_path();  // 获取Frenet坐标系下的路径点
    if (frenet_path.empty()) {                                // 如果Frenet路径为空
        AERROR << "Path is empty.";                           // 记录错误：路径为空
        return false;
    }
    // 获取车辆半宽，用于后续碰撞检测和避让距离计算
    const double half_width = common::VehicleConfigHelper::GetConfig().vehicle_param().width() / 2.0;
    // 横向忽略半径 = 车辆半宽 + 配置的横向忽略缓冲距离
    const double lateral_radius = half_width + FLAGS_lateral_ignore_buffer;

    // 遍历路径决策中所有的障碍物实例
    for (const auto *obstacle : path_decision->obstacles().Items()) {
        const std::string &obstacle_id = obstacle->Id();  // 获取障碍物ID
        // 获取障碍物感知类型的名称字符串，如 "VEHICLE", "PEDESTRIAN" 等
        const std::string obstacle_type_name = PerceptionObstacle_Type_Name(obstacle->Perception().type());
        // AINFO << "obstacle_id[" << obstacle_id << "] type[" << obstacle_type_name << "]";  // 输出调试信息

        // // 检查是否是ID为1000且类型为vehicle且速度小于3
        // if (obstacle_id == "1000_0" && obstacle_type_name == "VEHICLE" && obstacle->speed() < 3) {
        //     AINFO << "符合条件的障碍物，ID为1000，类型为VEHICLE，速度小于3，不跳过";
        //     // 不跳过，继续执行后续逻辑
        // } else if (!obstacle->IsStatic() || obstacle->IsVirtual()) {  // 如果障碍物不是静态的或者是虚拟的
        //     continue;                                          // 跳过当前障碍物
        // }
        // 如果障碍物不是静态的，或者是虚拟障碍物，则跳过当前循环，处理下一个障碍物
        if (!obstacle->IsStatic() || obstacle->IsVirtual()) {
            continue;
        }
        // 如果障碍物已经同时具有纵向和横向的IGNORE决策，则跳过
        if (obstacle->HasLongitudinalDecision() && obstacle->LongitudinalDecision().has_ignore()
            && obstacle->HasLateralDecision()
            && obstacle->LateralDecision().has_ignore()) {  // 如果障碍物已经有纵向和横向的忽略决策
            // AINFO << "id为" << obstacle_id << "的障碍物已有纵向和横向的忽略决策" ;
            continue;  // 跳过当前障碍物
        }
        if (obstacle->HasLongitudinalDecision()
            && obstacle->LongitudinalDecision().has_stop()) {  // 如果障碍物已经有停止的纵向决策
            // AINFO << "obstacle_id[" << obstacle_id << "] 已经有纵向的停止决策";  // 输出调试信息
            // STOP decision
            continue;  // 跳过当前障碍物
        }
        // 如果当前障碍物ID与先前识别的阻塞障碍物ID相同，
        // 并且车辆当前不处于借道场景 (is_in_path_lane_borrow_scenario 为 false)
        if (obstacle->Id() == blocking_obstacle_id
            && !injector_->planning_context()->planning_status().path_decider().is_in_path_lane_borrow_scenario()) {
            AINFO << "来自pathdecide判断不借道阻塞障碍物是 " << blocking_obstacle_id;  // 输出阻塞障碍物的调试信息
            ObjectDecisionType object_decision;                                        // 创建对象决策实例
            // 为此阻塞障碍物生成STOP决策详情
            *object_decision.mutable_stop() = GenerateObjectStopDecision(*obstacle);
            // 将STOP决策添加到路径决策中，原因为 "PathDecider/blocking_obstacle"
            path_decision->AddLongitudinalDecision("PathDecider/blocking_obstacle", obstacle->Id(), object_decision);
            continue;  // 处理下一个障碍物
        }
        // 如果障碍物位于需要保持畅通的区域 (KEEP_CLEAR zone)，则跳过
        if (obstacle->reference_line_st_boundary().boundary_type() == STBoundary::BoundaryType::KEEP_CLEAR) {
            continue;
        }

        // 0. 默认决策为IGNORE，特别是当障碍物在路径的S方向范围之外时
        ObjectDecisionType object_decision;                          // 创建新的对象决策实例
        object_decision.mutable_ignore();                            // 默认为IGNORE
        const auto &sl_boundary = obstacle->PerceptionSLBoundary();  // 获取障碍物在SL坐标系下的边界
        // 如果障碍物的S方向范围完全在当前Frenet路径的S范围之前或之后，则判定为不在路径上
        if (sl_boundary.end_s() < frenet_path.front().s() || sl_boundary.start_s() > frenet_path.back().s()) {
            // 添加纵向IGNORE决策，原因为 "PathDecider/not-in-s"
            path_decision->AddLongitudinalDecision("PathDecider/not-in-s", obstacle->Id(), object_decision);
            // 添加横向IGNORE决策，原因相同
            path_decision->AddLateralDecision("PathDecider/not-in-s", obstacle->Id(), object_decision);
            continue;  // 处理下一个障碍物
        }

        // 获取Frenet路径上离障碍物SL边界最近的点
        const auto frenet_point = frenet_path.GetNearestPoint(sl_boundary);
        const double curr_l = frenet_point.l();  // 该最近路径点的横向偏移 l值
        // 计算触发Nudge操作的最小横向距离阈值 (车辆半宽 + 静态障碍物缓冲距离的一半)
        double min_nudge_l = half_width + config_.static_obstacle_buffer() / 2.0;
        // AINFO << "sl_boundary.end_l() = " << sl_boundary.end_l();
        // AINFO << "curr_l  = " << curr_l;
        // AINFO << "sl_boundary.start_l() = " << sl_boundary.start_l();
        // AINFO << "min_nudge_l = " << min_nudge_l;
        // AINFO << "sl_boundary.end_l() > curr_l + min_nudge_l为" << (sl_boundary.end_l() > curr_l + min_nudge_l);
        // AINFO << "sl_boundary.start_l() < curr_l - min_nudge_l为" << (sl_boundary.start_l() < curr_l - min_nudge_l);
        // 根据障碍物与路径的横向位置关系进行决策
        if (curr_l - lateral_radius > sl_boundary.end_l() || curr_l + lateral_radius < sl_boundary.start_l()) {
            // 1. IGNORE: 如果障碍物在横向L方向上离路径中心线太远 (超出 lateral_radius 范围)
            path_decision->AddLateralDecision("PathDecider/not-in-l", obstacle->Id(), object_decision);
        } else if (
                sl_boundary.end_l() - config_.seat_buffer() >= curr_l - min_nudge_l
                && sl_boundary.start_l() + config_.seat_buffer() <= curr_l + min_nudge_l) {
            // 2. STOP: 如果障碍物在横向L方向上与路径存在显著重叠 (在 min_nudge_l 定义的碰撞风险区内)
            if (config_.skip_overlap_stop_check()) {  // 检查配置项，是否跳过因重叠导致的STOP决策
                // AINFO << "skip_overlap_stop_check";   // 信息日志：执行了跳过重叠停车检查的逻辑
                ;
            } else {  // 如果不跳过检查，则生成STOP决策
                *object_decision.mutable_stop() = GenerateObjectStopDecision(*obstacle);

                // 尝试将当前障碍物的STOP决策与主停车点（如果存在）合并
                // 如果合并成功（即当前障碍物是更近或更主要的停车原因）
                if (path_decision->MergeWithMainStop(
                            object_decision.stop(),
                            obstacle->Id(),
                            reference_line_info_->reference_line(),
                            reference_line_info_->AdcSlBoundary())) {
                    // 添加纵向STOP决策，原因为 "PathDecider/nearest-stop"
                    path_decision->AddLongitudinalDecision("PathDecider/nearest-stop", obstacle->Id(), object_decision);
                } else {                                 // 如果合并失败 (即存在其他更优先的停车点)
                    ObjectDecisionType object_decision;  // 创建新的决策实例
                    object_decision.mutable_ignore();    // 对此障碍物设为IGNORE (因为有其他更重要的停车决策)
                    path_decision->AddLongitudinalDecision(
                            "PathDecider/not-nearest-stop", obstacle->Id(), object_decision);
                }
                AINFO << "Add stop decision for static obs " << obstacle->Id() << "start l" << sl_boundary.start_l()
                      << "end l" << sl_boundary.end_l() << "curr_l" << curr_l << "min_nudge_l"
                      << min_nudge_l;  // 信息日志：记录添加STOP决策的详细参数
            }
        } else {
            // 3. NUDGE: 如果障碍物在横向L方向上较近，但未达到STOP的程度，考虑进行Nudge（微调路径以避开）
            AINFO << "NUDGE" << config_.seat_buffer();
            if (sl_boundary.end_l() - config_.seat_buffer() < curr_l - min_nudge_l) {  // &&
                // sl_boundary.end_l() > curr_l - min_nudge_l - 0.3) {
                // 障碍物在自车路径的左侧，且需要向左Nudge以保持安全距离
                ObjectNudge *object_nudge_ptr = object_decision.mutable_nudge();
                object_nudge_ptr->set_type(ObjectNudge::LEFT_NUDGE);  // 设置Nudge类型为向左
                // 设置Nudge的横向距离，通常为配置的静态障碍物缓冲距离
                object_nudge_ptr->set_distance_l(config_.static_obstacle_buffer());
                path_decision->AddLateralDecision("PathDecider/left-nudge", obstacle->Id(), object_decision);
                // path_decision->AddLongitudinalDecision("PathDecider/not-in-s", obstacle->Id(), object_decision);
                AINFO << "id为" << obstacle_id << "的障碍物设置为左nudge决策";
            } else if (sl_boundary.start_l() + config_.seat_buffer() > curr_l + min_nudge_l) {  // &&
                // sl_boundary.start_l() < curr_l + min_nudge_l + 0.3) {
                // 障碍物在自车路径的右侧，且需要向右Nudge以保持安全距离
                ObjectNudge *object_nudge_ptr = object_decision.mutable_nudge();
                object_nudge_ptr->set_type(ObjectNudge::RIGHT_NUDGE);  // 设置Nudge类型为向右
                // 设置Nudge的横向距离 (负值表示向右)
                object_nudge_ptr->set_distance_l(-config_.static_obstacle_buffer());
                path_decision->AddLateralDecision("PathDecider/right-nudge", obstacle->Id(), object_decision);
                AINFO << "id为" << obstacle_id << "的障碍物设置为右nudge决策";
            }
            // 注意：如果障碍物横向位置介于Nudge和Stop之间，或不满足特定Nudge条件，可能不会生成Nudge决策，
            // 此时可能依赖路径优化器自行处理，或者默认的IGNORE（如果之前设置了）会生效。
        }
    }

    return true;  // 静态障碍物决策处理完成
}

// 生成针对特定障碍物的STOP决策详情
ObjectStop PathDecider::GenerateObjectStopDecision(const Obstacle &obstacle) const {
    ObjectStop object_stop;  // 创建ObjectStop对象实例

    // 计算最小安全停车距离 (考虑车辆最小转弯半径等因素)
    double stop_distance = obstacle.MinRadiusStopDistance(VehicleConfigHelper::GetConfig().vehicle_param());
    object_stop.set_reason_code(StopReasonCode::STOP_REASON_OBSTACLE);  // 设置停车原因为障碍物
    // 设置沿参考线方向的停车距离，相对于障碍物SL边界的start_s，负值表示在障碍物前停车
    AINFO << "stop obstacle: " << obstacle.Id() << " stop_distance: " << stop_distance;  // 输出停止决策的障碍物信息
    object_stop.set_distance_s(-stop_distance);

    // 计算停车点在参考线上的s值 (障碍物前端s值 - 安全停车距离)
    const double stop_ref_s = obstacle.PerceptionSLBoundary().start_s() - stop_distance;
    // 根据s值获取参考线上的具体停车点坐标和朝向
    const auto stop_ref_point = reference_line_info_->reference_line().GetReferencePoint(stop_ref_s);
    object_stop.mutable_stop_point()->set_x(stop_ref_point.x());  // 设置停车点的世界坐标x
    object_stop.mutable_stop_point()->set_y(stop_ref_point.y());  // 设置停车点的世界坐标y
    object_stop.set_stop_heading(stop_ref_point.heading());       // 设置停车点的朝向

    return object_stop;  // 返回填充好的ObjectStop对象
}

// 忽略后方障碍物的逻辑 (主要针对非静态、非虚拟障碍物)
bool PathDecider::IgnoreBackwardObstacle(PathDecision *const path_decision) {
    // 获取自车当前SL坐标系下的起始s值 (车头位置)
    double adc_start_s = reference_line_info_->AdcSlBoundary().start_s();
    // 遍历路径决策中的所有障碍物
    for (const auto *obstacle : path_decision->obstacles().Items()) {
        // 如果是静态障碍物或虚拟障碍物，则跳过 (此函数主要处理动态障碍物)
        if (obstacle->IsStatic() || obstacle->IsVirtual()) {
            continue;
        }
        // 如果障碍物的SL边界的末端s值 (离自车最远的一端) 小于自车的起始s值，
        // 则认为该障碍物完全位于自车后方。
        if (obstacle->Obstacle::PerceptionSLBoundary().end_s() < adc_start_s) {
            ObjectDecisionType object_decision;  // 创建对象决策实例
            object_decision.mutable_ignore();    // 设置为IGNORE决策
            // 添加纵向IGNORE决策，原因为 "PathDecider/ignore-backward-obstacle"
            path_decision->AddLongitudinalDecision(
                    "PathDecider/ignore-backward-obstacle", obstacle->Id(), object_decision);
        }
    }
    return true;  // 处理完成
}

}  // namespace planning
}  // namespace apollo