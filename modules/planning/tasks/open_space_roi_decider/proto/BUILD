## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("//tools:python_rules.bzl", "py_proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "open_space_roi_decider_proto",
    srcs = ["open_space_roi_decider.proto"],
)

apollo_package()
