## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("//tools:python_rules.bzl", "py_proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "open_space_trajectory_provider_proto",
    srcs = ["open_space_trajectory_provider.proto"],
    deps = [
        "//modules/planning/planning_base/proto/math:fem_pos_deviation_smoother_config_proto",
        "//modules/planning/planning_open_space/proto:planner_open_space_config_proto"
    ],
)

apollo_package()
