open_space_trajectory_optimizer_config {
    planner_open_space_config {
    warm_start_config {
        xy_grid_resolution: 0.3
        phi_grid_resolution: 0.1
        next_node_num: 10
        step_size: 0.25
        traj_forward_penalty: 1.0
        traj_back_penalty: 1.0
        traj_gear_switch_penalty: 10.0
        traj_steer_penalty: 0.0
        traj_steer_change_penalty: 0.0
        grid_a_star_xy_resolution: 0.25
        node_radius: 0.5
    }
    dual_variable_warm_start_config {
        weight_d: 1.0
        ipopt_config {
        ipopt_print_level: 0
        mumps_mem_percent: 6000
        mumps_pivtol: 1e-06
        ipopt_max_iter: 100
        ipopt_tol: 1e-05
        ipopt_acceptable_constr_viol_tol: 0.1
        ipopt_min_hessian_perturbation: 1e-12
        ipopt_jacobian_regularization_value: 1e-07
        ipopt_print_timing_statistics: "yes"
        ipopt_alpha_for_y: "min"
        ipopt_recalc_y: "yes"
        }
        qp_format: OSQP
        min_safety_distance: 0.01
        osqp_config {
        alpha: 1.0
        eps_abs: 1.0e-3
        eps_rel: 1.0e-3
        max_iter: 10000
        polish: true
        osqp_debug_log: false
        }
    }
    distance_approach_config {
        weight_steer: 0.3
        weight_a: 1.1
        weight_steer_rate: 3.0
        weight_a_rate: 2.5
        weight_x: 2.3
        weight_y: 0.7
        weight_phi: 1.5
        weight_v: 0.0
        weight_steer_stitching: 1.75
        weight_a_stitching: 3.25
        weight_first_order_time: 4.25
        weight_second_order_time: 13.5
        weight_end_state: 1.0
        weight_slack: 1.0
        min_safety_distance: 0.01
        max_speed_forward: 2.0
        max_speed_reverse: 1.0
        max_acceleration_forward: 2.0
        max_acceleration_reverse: 1.0
        min_time_sample_scaling: 0.8
        max_time_sample_scaling: 1.2
        use_fix_time: false
        ipopt_config {
        ipopt_print_level: 0
        mumps_mem_percent: 6000
        mumps_pivtol: 1e-06
        ipopt_max_iter: 1000
        ipopt_tol: 0.0001
        ipopt_acceptable_constr_viol_tol: 0.1
        ipopt_min_hessian_perturbation: 1e-12
        ipopt_jacobian_regularization_value: 1e-07
        ipopt_print_timing_statistics: "yes"
        ipopt_alpha_for_y: "min"
        ipopt_recalc_y: "yes"
        ipopt_mu_init: 0.1
        }
        enable_constraint_check: false
        enable_initial_final_check: false
        enable_jacobian_ad: false
        enable_hand_derivative: false
        enable_derivative_check: false
        distance_approach_mode: DISTANCE_APPROACH_IPOPT_RELAX_END_SLACK
        enable_check_initial_state: false
    }
    iterative_anchoring_smoother_config {
        interpolated_delta_s: 0.1
        reanchoring_trails_num: 50
        reanchoring_pos_stddev: 0.25
        reanchoring_length_stddev: 1.0
        estimate_bound: false
        default_bound: 2.0
        vehicle_shortest_dimension: 1.04
        fem_pos_deviation_smoother_config {
        use_sqp: true
        weight_fem_pos_deviation: 1e7
        weight_path_length: 0.0
        weight_ref_deviation: 1e3
        apply_curvature_constraint: true
        weight_curvature_constraint_slack_var: 1e8
        curvature_constraint: 0.18
        max_iter: 500
        time_limit: 0.0
        verbose: false
        scaled_termination: true
        warm_start: true
        }
        s_curve_config {
        acc_weight: 1.0
        jerk_weight: 1.0
        kappa_penalty_weight: 100.0
        ref_s_weight: 10.0
        ref_v_weight: 0.0
        }
        collision_decrease_ratio: 0.0
        max_forward_v: 1.0
        max_reverse_v: 1.0
        max_forward_acc: 0.5
        max_reverse_acc: 1.0
        max_acc_jerk: 0.5
        delta_t: 0.2
    }
    delta_t: 0.5
    near_destination_threshold: 0.1
    enable_linear_interpolation: false
    is_near_destination_theta_threshold: 0.1
    }
}
open_space_planning_period: 0.1
open_space_trajectory_stitching_preserved_length: inf
open_space_standstill_acceleration: 0.3
