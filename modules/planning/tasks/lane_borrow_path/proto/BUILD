## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("//tools:python_rules.bzl", "py_proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "lane_borrow_path_proto",
    srcs = ["lane_borrow_path.proto"],
    deps = [
        "//modules/planning/planning_base/proto:piecewise_jerk_path_config_proto",
    ],
)

apollo_package()
