# Define all coms in DAG streaming.
module_config {
  module_library : "modules/planning/planning_component/libplanning_component.so"
  components {
    class_name : "PlanningComponent"
    config {
      name: "planning"
      config_file_path:  "modules/planning/planning_component/conf/planning_config.pb.txt"
      flag_file_path:  "modules/planning/planning_component/conf/planning.conf"
      readers: [
        {
          channel: "/apollo/prediction"
        },
        {
          channel: "/apollo/canbus/chassis"
          qos_profile: {
              depth : 15
          }
          pending_queue_size: 50
        },
        {
          channel: "/apollo/localization/pose"
          qos_profile: {
              depth : 15
          }
          pending_queue_size: 50
        }
      ]
    }
  }
}
