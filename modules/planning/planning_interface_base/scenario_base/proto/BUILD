## Auto generated by `proto_build_generator.py`
load("//tools:apollo_package.bzl", "apollo_package")
load("//tools/proto:proto.bzl", "proto_library")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "scenario_pipeline_proto",
    srcs = ["scenario_pipeline.proto"],
    deps = [
        "//modules/planning/planning_base/proto:plugin_declare_info_proto",
    ]
)

proto_library(
    name = "creep_stage_proto",
    srcs = ["creep_stage.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

apollo_package()
