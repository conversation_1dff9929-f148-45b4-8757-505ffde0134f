/******************************************************************************
 * Copyright 2019 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

/**
 * @file base_stage_traffic_light_cruise.cc
 **/

#include "modules/planning/planning_interface_base/scenario_base/traffic_light_base/base_stage_traffic_light_cruise.h"

namespace apollo {
namespace planning {

hdmap::PathOverlap* BaseStageTrafficLightCruise::GetTrafficSignOverlap(
    const ReferenceLineInfo& reference_line_info,
    const PlanningContext* context) const {
  // traffic_light scenarios
  const auto& traffic_light_status = context->planning_status().traffic_light();
  const std::string traffic_sign_overlap_id =
      traffic_light_status.current_traffic_light_overlap_id_size() > 0
          ? traffic_light_status.current_traffic_light_overlap_id(0)
          : "";
  auto traffic_sign_overlap = reference_line_info.GetOverlapOnReferenceLine(
      traffic_sign_overlap_id, ReferenceLineInfo::SIGNAL);
  return traffic_sign_overlap;
}

}  // namespace planning
}  // namespace apollo
