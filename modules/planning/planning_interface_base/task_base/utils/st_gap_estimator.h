/******************************************************************************
 * Copyright 2019 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

/**
 * @file
 * @brief Defines proper safety distance the ADC should keep from target
 * obstacle considering different information.
 **/

namespace apollo {
namespace planning {
class StGapEstimator {
 public:
  StGapEstimator() = delete;

  virtual ~StGapEstimator() = delete;

  static double EstimateSafeOvertakingGap();

  static double EstimateSafeFollowingGap(const double target_obs_speed);

  static double EstimateSafeYieldingGap();

  static double EstimateProperOvertakingGap(const double target_obs_speed,
                                            const double adc_speed);

  static double EstimateProperFollowingGap(const double adc_speed);

  static double EstimateProperYieldingGap();
};
}  // namespace planning
}  // namespace apollo
