header {
  timestamp_sec: **********.9515159
  module_name: "routing"
  sequence_num: 2
}
road {
  id: "23-22-7-21-1-151-88-13-6-12-9-133"
  passage {
    segment {
      id: "6_1_-2"
      start_s: 25.***************
      end_s: 194.692
    }
    segment {
      id: "7_1_-2"
      start_s: 0
      end_s: 32.89
    }
    segment {
      id: "1_1_-3"
      start_s: 0
      end_s: 65.2454
    }
    segment {
      id: "151_1_-2"
      start_s: 0
      end_s: 43.8648
    }
    segment {
      id: "9_1_-2"
      start_s: 0
      end_s: 161.962
    }
    segment {
      id: "13_1_-2"
      start_s: 0
      end_s: 35.3398
    }
    segment {
      id: "12_1_-3"
      start_s: 0
      end_s: 122.494
    }
    segment {
      id: "133_1_-3"
      start_s: 0
      end_s: 3.63182
    }
    segment {
      id: "88_1_-1"
      start_s: 0
      end_s: 19.7333
    }
    segment {
      id: "21_1_-1"
      start_s: 0
      end_s: 128.73
    }
    segment {
      id: "22_1_-1"
      start_s: 0
      end_s: 31.9409
    }
    segment {
      id: "23_1_-1"
      start_s: 0
      end_s: 1.6278812804440195e-07
    }
    can_exit: true
    change_lane_type: FORWARD
  }
}
measurement {
  distance: 815.**************
}
routing_request {
  header {
    timestamp_sec: **********.9473982
    module_name: "dreamview"
    sequence_num: 2
  }
  waypoint {
    id: "6_1_-2"
    s: 25.***************
    pose {
      x: 587481.*********
      y: 4140747.********
    }
  }
  waypoint {
    id: "23_1_-1"
    s: 1.6278812804440195e-07
    pose {
      x: 586948.74012
      y: 4141171.118641
    }
  }
}
map_version: "1.000000"
status {
  error_code: OK
  msg: "Success!"
}
