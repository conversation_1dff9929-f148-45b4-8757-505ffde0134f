header {
  timestamp_sec: **********.1569996
  module_name: "routing"
  sequence_num: 3
}
road {
  id: "57"
  passage {
    segment {
      id: "57_1_-1"
      start_s: 164.**************
      end_s: 285.**************
    }
    can_exit: true
    change_lane_type: FORWARD
  }
}
measurement {
  distance: 120.**************
}
routing_request {
  header {
    timestamp_sec: **********.1533203
    module_name: "dreamview"
    sequence_num: 3
  }
  waypoint {
    id: "57_1_-1"
    s: 164.**************
    pose {
      x: 587583.*********
      y: 4140874.**********
    }
  }
  waypoint {
    id: "57_1_-1"
    s: 285.**************
    pose {
      x: 587613.**********
      y: 4140990.**********
    }
  }
}
map_version: "1.000000"
status {
  error_code: OK
  msg: "Success!"
}
