header {
  timestamp_sec: **********.88418
  module_name: "routing"
  sequence_num: 2
}
road {
  id: "42-157-40"
  passage {
    segment {
      id: "40_1_-3"
      start_s: 7.**************
      end_s: 593.196
    }
    change_lane_type: LEFT
  }
  passage {
    segment {
      id: "40_1_-2"
      start_s: 7.****************
      end_s: 593.037
    }
    change_lane_type: LEFT
  }
  passage {
    segment {
      id: "40_1_-1"
      start_s: 7.**************
      end_s: 592.956
    }
    segment {
      id: "157_1_-1"
      start_s: 0
      end_s: 37.8509
    }
    segment {
      id: "42_1_-1"
      start_s: 0
      end_s: 18.***************
    }
    can_exit: true
    change_lane_type: FORWARD
  }
}
measurement {
  distance: 642.**************
}
routing_request {
  header {
    timestamp_sec: **********.8777616
    module_name: "dreamview"
    sequence_num: 1
  }
  waypoint {
    id: "40_1_-3"
    s: 7.**************
    pose {
      x: 587703.***********
      y: 4141469.**********
    }
  }
  waypoint {
    id: "42_1_-1"
    s: 18.***************
    pose {
      x: 587072.***********
      y: 4141576.**********
    }
  }
}
map_version: "1.000000"
status {
  error_code: OK
  msg: "Success!"
}
