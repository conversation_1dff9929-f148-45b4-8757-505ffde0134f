header {
  module_name: "planning"
}
total_path_length: 0
total_path_time: 6.999999999999992
is_replan: true
gear: GEAR_DRIVE
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.02
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.04
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.06
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.08
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.1
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.12000000000000001
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.14
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.16
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.18
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.19999999999999998
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.21999999999999997
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.23999999999999996
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.25999999999999995
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.27999999999999997
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.3
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.32
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.34
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.36000000000000004
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.38000000000000006
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.40000000000000008
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.4200000000000001
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.44000000000000011
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.46000000000000013
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.48000000000000015
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.50000000000000011
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.52000000000000013
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.54000000000000015
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.56000000000000016
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.58000000000000018
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.6000000000000002
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.62000000000000022
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.64000000000000024
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.66000000000000025
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.68000000000000027
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.70000000000000029
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.72000000000000031
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.74000000000000032
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.76000000000000034
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.78000000000000036
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.80000000000000038
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.8200000000000004
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.84000000000000041
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.86000000000000043
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.88000000000000045
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.90000000000000047
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.92000000000000048
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.9400000000000005
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.96000000000000052
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 0.98000000000000054
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.0000000000000004
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.1000000000000005
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.2000000000000006
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.3000000000000007
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.4000000000000008
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.5000000000000009
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.600000000000001
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.7000000000000011
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.8000000000000012
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 1.9000000000000012
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.0000000000000013
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.1000000000000014
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.2000000000000015
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.3000000000000016
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.4000000000000017
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.5000000000000018
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.6000000000000019
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.700000000000002
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.800000000000002
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 2.9000000000000021
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.0000000000000022
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.1000000000000023
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.2000000000000024
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.3000000000000025
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.4000000000000026
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.5000000000000027
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.6000000000000028
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.7000000000000028
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.8000000000000029
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 3.900000000000003
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.0000000000000027
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.1000000000000023
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.200000000000002
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.3000000000000016
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.4000000000000012
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.5000000000000009
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.6000000000000005
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.7
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.8
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.8999999999999995
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 4.9999999999999991
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.0999999999999988
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.1999999999999984
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.299999999999998
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.3999999999999977
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.4999999999999973
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.599999999999997
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.6999999999999966
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.7999999999999963
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.8999999999999959
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 5.9999999999999956
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.0999999999999952
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.1999999999999948
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.2999999999999945
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.3999999999999941
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.4999999999999938
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.5999999999999934
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.6999999999999931
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.7999999999999927
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.8999999999999924
}
trajectory_point {
  path_point {
    x: 587045.66208016046
    y: 4141549.0618808549
    z: 0
    theta: 1.2103928991526534
    kappa: -0.011166695765584999
    s: 0
    dkappa: 0
    ddkappa: 0
  }
  v: 0
  a: 0
  relative_time: 6.999999999999992
}
decision {
  main_decision {
    stop {
      reason_code: STOP_REASON_OBSTACLE
      reason: "stop by 401_0"
      stop_point {
        x: 587047.77270419174
        y: 4141552.3894470483
      }
      stop_heading: 1.145872771763222
      change_lane_type: FORWARD
    }
  }
  object_decision {
    decision {
      id: "401_0"
      perception_id: 401
      object_decision {
        stop {
          reason_code: STOP_REASON_OBSTACLE
          distance_s: -6
          stop_point {
            x: 587047.77270419174
            y: 4141552.3894470483
            z: 0
          }
          stop_heading: 1.145872771763222
        }
      }
    }
    decision {
      id: "401_1"
      perception_id: 401
      object_decision {
        stop {
          reason_code: STOP_REASON_OBSTACLE
          distance_s: -6
          stop_point {
            x: 587047.77270419174
            y: 4141552.3894470483
            z: 0
          }
          stop_heading: 1.145872771763222
        }
      }
    }
    decision {
      id: "401_2"
      perception_id: 401
      object_decision {
        stop {
          reason_code: STOP_REASON_OBSTACLE
          distance_s: -6
          stop_point {
            x: 587047.77270419174
            y: 4141552.3894470483
            z: 0
          }
          stop_heading: 1.145872771763222
        }
      }
    }
    decision {
      id: "SL_1065"
      perception_id: -436175050
      object_decision {
        stop {
          reason_code: STOP_REASON_SIGNAL
          distance_s: -1
          stop_point {
            x: 587051.21824032988
            y: 4141558.**********
            z: 0
          }
          stop_heading: 0.****************
        }
      }
    }
    decision {
      id: "SL_1076"
      perception_id: -*********
      object_decision {
        stop {
          reason_code: STOP_REASON_OBSTACLE
          distance_s: -6
          stop_point {
            x: 587048.***********
            y: 4141554.**********
          }
          stop_heading: 1.****************
        }
      }
    }
  }
  vehicle_signal {
    turn_signal: TURN_RIGHT
  }
}
routing_header {
  timestamp_sec: **********.0536358
  module_name: "routing"
}
right_of_way_status: UNPROTECTED
lane_id {
  id: "28_1_-1"
}
lane_id {
  id: "27_1_-1"
}
lane_id {
  id: "81_1_-1"
}
lane_id {
  id: "160_1_-3"
}
engage_advice {
  advice: READY_TO_ENGAGE
}
