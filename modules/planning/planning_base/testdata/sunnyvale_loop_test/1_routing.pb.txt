header {
  timestamp_sec: **********.52
  module_name: "routing"
  sequence_num: 1
}
road {
  id: "r0"
  passage {
    can_exit: true
    segment {
      id: "752_1_-1"
      start_s: 0.0
      end_s: 20.1963
    }
  }
}
road {
  id: "r1"
  passage {
    can_exit: true
    segment {
      id: "753_1_-1"
      start_s: 0.0
      end_s: 21.6437
    }
  }
}
road {
  id: "r2"
  passage {
    can_exit: true
    segment {
      id: "6_1_-2"
      start_s: 0.0
      end_s: 194.692
    }
  }
}
road {
  id: "r3"
  passage {
    can_exit: true
    segment {
      id: "7_1_-2"
      start_s: 0.0
      end_s: 32.89
    }
  }
}
road {
  id: "r4"
  passage {
    can_exit: true
    segment {
      id: "1_1_-3"
      start_s: 0.0
      end_s: 65.2454
    }
  }
}
road {
  id: "r5"
  passage {
    can_exit: true
    segment {
      id: "151_1_-2"
      start_s: 0.0
      end_s: 43.8648
    }
  }
}
road {
  id: "r6"
  passage {
    can_exit: true
    segment {
      id: "9_1_-2"
      start_s: 0.0
      end_s: 161.962
    }
  }
}
road {
  id: "r7"
  passage {
    can_exit: true
    segment {
      id: "13_1_-2"
      start_s: 0.0
      end_s: 35.3398
    }
  }
}
road {
  id: "r8"
  passage {
    can_exit: true
    segment {
      id: "12_1_-3"
      start_s: 0.0
      end_s: 122.494
    }
  }
}
road {
  id: "r9"
  passage {
    can_exit: true
    segment {
      id: "133_1_-3"
      start_s: 0.0
      end_s: 3.63182
    }
  }
}
road {
  id: "r10"
  passage {
    can_exit: true
    segment {
      id: "88_1_-1"
      start_s: 0.0
      end_s: 19.7333
    }
  }
}
road {
  id: "r11"
  passage {
    can_exit: true
    segment {
      id: "21_1_-1"
      start_s: 0.0
      end_s: 128.73
    }
  }
}
road {
  id: "r12"
  passage {
    can_exit: true
    segment {
      id: "22_1_-1"
      start_s: 0.0
      end_s: 31.9409
    }
  }
}
road {
  id: "r13"
  passage {
    can_exit: true
    segment {
      id: "23_1_-1"
      start_s: 0.0
      end_s: 0.0
    }
  }
  passage {
    can_exit: true
    segment {
      id: "23_1_-2"
      start_s: 0.0
      end_s: 0.0
    }
  }
}
measurement {
  distance: 882.36402
}
routing_request {
  header {
    timestamp_sec: **********.52
    module_name: "hmi_ros_bridge"
    sequence_num: 8
  }
  waypoint {
    id: "752_1_-1"
    s: 19.**********
    pose {
      x: 587513.524212
      y: 4140713.13848
      z: -29.**********
    }
  }
  waypoint {
    id: "23_1_-2"
    s: 0.0
    pose {
      x: 586951.020771
      y: 4141170.53658
    }
  }
}
map_version: "1.400000"
