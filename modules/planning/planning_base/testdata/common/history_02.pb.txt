header {
  sequence_num: 2
  timestamp_sec: 1517275936.5507183
  lidar_timestamp: 0
  camera_timestamp: 0
  radar_timestamp: 0
}
is_replan: true
gear: GEAR_DRIVE
decision {
  main_decision {
    stop {
      reason_code: STOP_REASON_CROSSWALK
      reason: "stop by CW_2832"
      stop_point {
        x: 586261.32728367555
        y: 4141304.5688100113
      }
      stop_heading: -1.****************
      change_lane_type: FORWARD
    }
  }
  object_decision {
    decision {
      id: "11511_0"
      perception_id: 11511
      object_decision {
        follow {
          distance_s: -3
          fence_point {
            x: 586260.940854132
            y: 4141282.23052737
            z: 0
          }
          fence_heading: -1.2301434845598906
        }
      }
    }
    decision {
      id: "11511_1"
      perception_id: 11511
      object_decision {
        follow {
          distance_s: -3
          fence_point {
            x: 586260.87535046809
            y: 4141282.4194931639
            z: 0
          }
          fence_heading: -1.2478322159946527
        }
      }
    }
    decision {
      id: "11652"
      perception_id: 11652
      object_decision {
        nudge {
          type: RIGHT_NUDGE
          distance_l: -0.3
        }
      }
    }
    decision {
      id: "CW_2832"
      perception_id: -1198750198
      object_decision {
        stop {
          reason_code: STOP_REASON_CROSSWALK
          distance_s: -1
          stop_point {
            x: 586261.32728367555
            y: 4141304.5688100113
            z: 0
          }
          stop_heading: -1.****************
          wait_for_obstacle: "11652"
        }
      }
    }
    decision {
      id: "TL_2516"
      perception_id: -*********
      object_decision {
        stop {
          reason_code: STOP_REASON_SIGNAL
          distance_s: -1
          stop_point {
            x: 586261.***********
            y: 4141304.**********
            z: 0
          }
          stop_heading: -1.****************
        }
      }
    }
  }
  vehicle_signal {
    turn_signal: TURN_LEFT
  }
}
routing_header {
  timestamp_sec: **********.387177
  module_name: "routing"
  sequence_num: 3
}
right_of_way_status: UNPROTECTED
lane_id {
  id: "188_1_-1"
}
lane_id {
  id: "2726_1_-1"
}
lane_id {
  id: "382_1_-1"
}
lane_id {
  id: "10898_1_-1"
}
lane_id {
  id: "300_1_-1"
}
lane_id {
  id: "10771_1_-1"
}
lane_id {
  id: "201_1_-1"
}
lane_id {
  id: "2811_1_-1"
}
lane_id {
  id: "2813_1_-1"
}
engage_advice {
  advice: READY_TO_ENGAGE
}
trajectory_type: SPEED_FALLBACK
replan_reason: "replan for no previous trajectory."
