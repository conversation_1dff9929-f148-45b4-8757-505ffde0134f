header {
  timestamp_sec: 1517275936.5507183
  lidar_timestamp: 0
  camera_timestamp: 0
  radar_timestamp: 0
}
is_replan: true
gear: GEAR_DRIVE
decision {
  main_decision {
    stop {
      reason_code: STOP_REASON_SIGNAL
      reason: "stop by TL_2516"
      stop_point {
        x: 586261.***********
        y: 4141304.**********
      }
      stop_heading: -1.****************
      change_lane_type: FORWARD
    }
  }
  object_decision {
    decision {
      id: "11511_0"
      perception_id: 11511
      object_decision {
        yield {
          distance_s: -5
          fence_point {
            x: 586260.39542419114
            y: 4141284.1379236
            z: 0
          }
          fence_heading: -1.3175529905840133
        }
      }
    }
    decision {
      id: "11511_1"
      perception_id: 11511
      object_decision {
        yield {
          distance_s: -5
          fence_point {
            x: 586260.3467864691
            y: 4141284.3319181586
            z: 0
          }
          fence_heading: -1.****************
        }
      }
    }
    decision {
      id: "TL_2516"
      perception_id: -*********
      object_decision {
        stop {
          reason_code: STOP_REASON_SIGNAL
          distance_s: -1
          stop_point {
            x: 586261.***********
            y: 4141304.**********
            z: 0
          }
          stop_heading: -1.****************
        }
      }
    }
  }
  vehicle_signal {
    turn_signal: TURN_LEFT
  }
}
routing_header {
  timestamp_sec: **********.387177
  module_name: "routing"
  sequence_num: 3
}
right_of_way_status: UNPROTECTED
lane_id {
  id: "188_1_-1"
}
lane_id {
  id: "2726_1_-1"
}
lane_id {
  id: "382_1_-1"
}
lane_id {
  id: "10898_1_-1"
}
lane_id {
  id: "300_1_-1"
}
lane_id {
  id: "10771_1_-1"
}
lane_id {
  id: "201_1_-1"
}
lane_id {
  id: "2811_1_-1"
}
lane_id {
  id: "2813_1_-1"
}
engage_advice {
  advice: READY_TO_ENGAGE
}
trajectory_type: SPEED_FALLBACK
replan_reason: "replan for manual mode."
target_lane_id {
  id: "188_1_-1"
}
target_lane_id {
  id: "2726_1_-1"
}
target_lane_id {
  id: "382_1_-1"
}
target_lane_id {
  id: "10898_1_-1"
}
target_lane_id {
  id: "300_1_-1"
}
target_lane_id {
  id: "10771_1_-1"
}
target_lane_id {
  id: "201_1_-1"
}
target_lane_id {
  id: "2811_1_-1"
}
target_lane_id {
  id: "2813_1_-1"
}
