header {
  timestamp_sec: **********.1240928
  lidar_timestamp: 0
  camera_timestamp: 0
  radar_timestamp: 0
}
is_replan: true
gear: GEAR_DRIVE
decision {
  main_decision {
    cruise {
      change_lane_type: FORWARD
    }
  }
  object_decision {
  }
  vehicle_signal {
    turn_signal: TURN_NONE
  }
}
routing_header {
  timestamp_sec: **********.2057884
  module_name: "routing"
  sequence_num: 1
}
right_of_way_status: UNPROTECTED
lane_id {
  id: "2900_1_-1"
}
lane_id {
  id: "11043_1_-1"
}
lane_id {
  id: "2907_1_-1"
}
lane_id {
  id: "2906_1_-1"
}
lane_id {
  id: "10590_1_-1"
}
lane_id {
  id: "10589_1_-1"
}
lane_id {
  id: "2912_1_-1"
}
lane_id {
  id: "2911_1_-1"
}
lane_id {
  id: "2914_1_-1"
}
lane_id {
  id: "2919_1_-1"
}
lane_id {
  id: "2918_1_-1"
}
lane_id {
  id: "338_1_-1"
}
lane_id {
  id: "10557_1_-1"
}
lane_id {
  id: "11044_1_-1"
}
lane_id {
  id: "373_1_-1"
}
lane_id {
  id: "2923_1_-1"
}
lane_id {
  id: "2922_1_-1"
}
engage_advice {
  advice: READY_TO_ENGAGE
}
trajectory_type: NORMAL
replan_reason: "replan for no previous trajectory."
target_lane_id {
  id: "2900_1_-1"
}
target_lane_id {
  id: "11043_1_-1"
}
target_lane_id {
  id: "2907_1_-1"
}
target_lane_id {
  id: "2906_1_-1"
}
target_lane_id {
  id: "10590_1_-1"
}
target_lane_id {
  id: "10589_1_-1"
}
target_lane_id {
  id: "2912_1_-1"
}
target_lane_id {
  id: "2911_1_-1"
}
target_lane_id {
  id: "2914_1_-1"
}
target_lane_id {
  id: "2919_1_-1"
}
target_lane_id {
  id: "2918_1_-1"
}
target_lane_id {
  id: "338_1_-1"
}
target_lane_id {
  id: "10557_1_-1"
}
target_lane_id {
  id: "11044_1_-1"
}
target_lane_id {
  id: "373_1_-1"
}
target_lane_id {
  id: "2923_1_-1"
}
target_lane_id {
  id: "2922_1_-1"
}
