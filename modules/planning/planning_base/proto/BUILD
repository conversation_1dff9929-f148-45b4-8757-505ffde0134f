load("//tools:cpplint.bzl", "cpplint")

## Auto generated by `proto_build_generator.py`
load("//tools:apollo_package.bzl", "apollo_package")
load("//tools/proto:proto.bzl", "proto_library")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "st_drivable_boundary_proto",
    srcs = ["st_drivable_boundary.proto"],
)

proto_library(
    name = "planning_semantic_map_config_proto",
    srcs = ["planning_semantic_map_config.proto"],
)

proto_library(
    name = "planning_status_proto",
    srcs = ["planning_status.proto"],
    deps = [
        ":planning_config_proto",
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/external_command_msgs:lane_follow_command_proto",
        "//modules/common_msgs/routing_msgs:routing_proto",
    ],
)

proto_library(
    name = "lattice_structure_proto",
    srcs = ["lattice_structure.proto"],
)

proto_library(
    name = "learning_data_proto",
    srcs = ["learning_data.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:geometry_proto",
        "//modules/common_msgs/basic_msgs:header_proto",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common_msgs/chassis_msgs:chassis_proto",
        "//modules/common_msgs/map_msgs:map_lane_proto",
        "//modules/common_msgs/perception_msgs:perception_obstacle_proto",
        "//modules/common_msgs/perception_msgs:traffic_light_detection_proto",
        "//modules/common_msgs/prediction_msgs:feature_proto",
        "//modules/common_msgs/prediction_msgs:prediction_obstacle_proto",
        "//modules/common_msgs/routing_msgs:routing_geometry_proto",
    ],
)

proto_library(
    name = "auto_tuning_model_input_proto",
    srcs = ["auto_tuning_model_input.proto"],
)

proto_library(
    name = "auto_tuning_raw_feature_proto",
    srcs = ["auto_tuning_raw_feature.proto"],
    deps = [
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
    ],
)

proto_library(
    name = "reference_line_smoother_config_proto",
    srcs = ["reference_line_smoother_config.proto"],
    deps = [
        "//modules/planning/planning_base/proto/math:cos_theta_smoother_config_proto",
        "//modules/planning/planning_base/proto/math:fem_pos_deviation_smoother_config_proto",
    ],
)

proto_library(
    name = "ipopt_return_status_proto",
    srcs = ["ipopt_return_status.proto"],
)

proto_library(
    name = "planning_config_proto",
    srcs = ["planning_config.proto"],
    deps = [
        ":plugin_declare_info_proto",
    ],
)

proto_library(
    name = "plugin_declare_info_proto",
    srcs = [":plugin_declare_info.proto"],
)

proto_library(
    name = "piecewise_jerk_path_config_proto",
    srcs = ["piecewise_jerk_path_config.proto"],
)

apollo_package()

cpplint()
