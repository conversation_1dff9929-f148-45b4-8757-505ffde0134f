syntax = "proto2";

package apollo.planning;

import "modules/planning/planning_base/proto/math/cos_theta_smoother_config.proto";
import "modules/planning/planning_base/proto/math/fem_pos_deviation_smoother_config.proto";

message QpSplineSmootherConfig {
  optional uint32 spline_order = 1 [default = 5];
  optional double max_spline_length = 2 [default = 25];
  optional double regularization_weight = 3 [default = 0.1];
  optional double second_derivative_weight = 4 [default = 0.0];
  optional double third_derivative_weight = 5 [default = 100];
}

message SpiralSmootherConfig {
  // The max deviation of spiral reference line smoother.
  optional double max_deviation = 1 [default = 0.1];

  // The piecewise length of spiral smoother.
  optional double piecewise_length = 2 [default = 10.0];

  // The iteration num of spiral reference line smoother.");
  optional uint32 max_iteration = 3 [default = 1000];

  // The desired convergence tol for spiral opt;
  optional double opt_tol = 4 [default = 1.0e-8];

  // The acceptable convergence tol for spiral opt
  optional double opt_acceptable_tol = 5 [default = 1e-6];

  // The number of acceptable iters before termination for spiral opt;
  optional uint32 opt_acceptable_iteration = 6 [default = 15];

  // The weight of curve length term in objective function
  optional double weight_curve_length = 7 [default = 1.0];

  // The weight of kappa term in objective function
  optional double weight_kappa = 8 [default = 1.0];

  // The weight of dkappa term in objective function
  optional double weight_dkappa = 9 [default = 100.0];
}

message DiscretePointsSmootherConfig {
  enum SmoothingMethod {
    NOT_DEFINED = 0;
    COS_THETA_SMOOTHING = 1;
    FEM_POS_DEVIATION_SMOOTHING = 2;
  }

  optional SmoothingMethod smoothing_method = 3
      [default = FEM_POS_DEVIATION_SMOOTHING];

  oneof SmootherConfig {
    CosThetaSmootherConfig cos_theta_smoothing = 4;
    FemPosDeviationSmootherConfig fem_pos_deviation_smoothing = 5;
  }
}

message ReferenceLineSmootherConfig {
  // The output resolution for discrete point smoother reference line is
  // directly decided by max_constraint_interval
  optional double max_constraint_interval = 1 [default = 5];
  optional double longitudinal_boundary_bound = 2 [default = 1.0];
  optional double max_lateral_boundary_bound = 3 [default = 0.5];
  optional double min_lateral_boundary_bound = 4 [default = 0.2];
  // The output resolution for qp smoother reference line.
  optional uint32 num_of_total_points = 5 [default = 500];
  optional double curb_shift = 6 [default = 0.2];
  optional double lateral_buffer = 7 [default = 0.2];
  // The output resolution for spiral smoother reference line.
  optional double resolution = 8 [default = 0.02];
  oneof SmootherConfig {
    QpSplineSmootherConfig qp_spline = 20;
    SpiralSmootherConfig spiral = 21;
    DiscretePointsSmootherConfig discrete_points = 22;
  }
}
