## Auto generated by `proto_build_generator.py`
load("//tools:apollo_package.bzl", "apollo_package")
load("//tools/proto:proto.bzl", "proto_library")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "cos_theta_smoother_config_proto",
    srcs = ["cos_theta_smoother_config.proto"],
)

proto_library(
    name = "qp_problem_proto",
    srcs = ["qp_problem.proto"],
)

proto_library(
    name = "fem_pos_deviation_smoother_config_proto",
    srcs = ["fem_pos_deviation_smoother_config.proto"],
)

apollo_package()