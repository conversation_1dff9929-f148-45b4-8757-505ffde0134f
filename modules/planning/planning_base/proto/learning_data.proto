syntax = "proto2";

package apollo.planning;

import "modules/common_msgs/chassis_msgs/chassis.proto";
import "modules/common_msgs/basic_msgs/geometry.proto";
import "modules/common_msgs/basic_msgs/header.proto";
import "modules/common_msgs/basic_msgs/pnc_point.proto";
import "modules/common_msgs/map_msgs/map_lane.proto";
import "modules/common_msgs/perception_msgs/perception_obstacle.proto";
import "modules/common_msgs/prediction_msgs/feature.proto";
import "modules/common_msgs/prediction_msgs/prediction_obstacle.proto";
import "modules/common_msgs/perception_msgs/traffic_light_detection.proto";
import "modules/common_msgs/routing_msgs/geometry.proto";

message OverlapFeature {
  optional string id = 1;
  optional double distance = 2;
}

message PlanningTag {
  optional apollo.hdmap.Lane.LaneTurn lane_turn = 1;
  optional OverlapFeature clear_area = 2;
  optional OverlapFeature crosswalk = 3;
  optional OverlapFeature pnc_junction = 4;
  optional OverlapFeature signal = 5;
  optional OverlapFeature stop_sign = 6;
  optional OverlapFeature yield_sign = 7;
}

message ChassisFeature {
  optional double message_timestamp_sec = 1;

  // Features from chassis
  // Vehicle Speed in meters per second.
  optional float speed_mps = 2;

  // Real throttle location in [%], ranging from 0 to 100.
  optional float throttle_percentage = 3;

  // Real brake location in [%], ranging from 0 to 100.
  optional float brake_percentage = 4;

  // Real steering location in [%], ranging from -100 to 100.
  // steering_angle / max_steering_angle
  // Clockwise: negative
  // CountClockwise: positive
  optional float steering_percentage = 5;

  optional apollo.canbus.Chassis.GearPosition gear_location = 6;
}

message LocalizationFeature {
  optional double message_timestamp_sec = 1;

  // Position of the vehicle reference point (VRP) in the map reference frame.
  // The VRP is the center of rear axle.
  optional apollo.common.PointENU position = 2;

  // Heading
  // The heading is zero when the car is facing East and positive when facing
  // North.
  optional double heading = 3;

  // Linear velocity of the VRP in the map reference frame.
  // East/north/up in meters per second.
  optional apollo.common.Point3D linear_velocity = 4;

  // Linear acceleration of the VRP in the map reference frame.
  // East/north/up in meters per second.
  optional apollo.common.Point3D linear_acceleration = 5;

  // Angular velocity of the vehicle in the map reference frame.
  // Around east/north/up axes in radians per second.
  optional apollo.common.Point3D angular_velocity = 6;
}

// based on apollo.common.PathPoint
message CommonPathPointFeature {
  // coordinates
  optional double x = 1;
  optional double y = 2;
  optional double z = 3;

  // direction on the x-y plane
  optional double theta = 4;
  // accumulated distance from beginning of the path
  optional double s = 5;
  // The lane ID where the path point is on
  optional string lane_id = 6;
}

// based on apollo.commom.TrajectoryPoint
message CommonTrajectoryPointFeature {
  // path point
  optional CommonPathPointFeature path_point = 1;
  // linear velocity
  optional double v = 2;  // in [m/s]
  // linear acceleration
  optional double a = 3;
  // relative time from beginning of the trajectory
  optional double relative_time = 4;
  // Gaussian probability information
  optional apollo.common.GaussianInfo gaussian_info = 5;
}

message TrajectoryPointFeature {
  optional double timestamp_sec = 1;
  optional CommonTrajectoryPointFeature trajectory_point = 2;
}

// based on apollo.perception.PerceptionObstacle
message PerceptionObstacleFeature {
  optional double timestamp_sec = 1;  // GPS time in seconds

  // obstacle position in the OBJECT coordinate system
  optional apollo.common.Point3D position = 2;

  // heading in RELATIVE coordinate to ADC
  optional double theta = 3;

  // obstacle velocity in RELATIVE coordinate to ADC
  optional apollo.common.Point3D velocity = 4;  // obstacle velocity

  // obstacle acceleration in RELATIVE coordinate to ADC
  optional apollo.common.Point3D acceleration = 5;

  // obstacle corner points in RELATIVE coordinate to ADC
  repeated apollo.common.Point3D polygon_point = 6;
}

message ObstacleTrajectoryFeature {
  repeated PerceptionObstacleFeature perception_obstacle_history = 1;
  repeated TrajectoryPointFeature evaluated_trajectory_point = 2;
}

// based on apollo.prediction.Trajectory
message PredictionTrajectoryFeature {
  optional double probability = 1;  // probability of this trajectory
  repeated TrajectoryPointFeature trajectory_point = 2;
}

// based on apollo.prediction.PredictionObstacle
message PredictionObstacleFeature {
  optional double timestamp_sec = 1;  // GPS time in seconds
  optional double predicted_period = 2;
  optional apollo.prediction.ObstacleIntent intent = 3;
  optional apollo.prediction.ObstaclePriority priority = 4;
  optional bool is_static = 5 [default = false];

  // can have multiple trajectories per obstacle
  repeated PredictionTrajectoryFeature trajectory = 6;
}

message ObstacleFeature {
  optional int32 id = 1;       // obstacle ID.
  optional double length = 2;  // obstacle length.
  optional double width = 3;   // obstacle width.
  optional double height = 4;  // obstacle height.
  optional apollo.perception.PerceptionObstacle.Type type = 5;  // obstacle type
  optional ObstacleTrajectoryFeature obstacle_trajectory = 6;
  optional PredictionObstacleFeature obstacle_prediction = 7;
}

// based on apollo.routing.RoutingResponse
message RoutingResponseFeature {
  repeated apollo.routing.RoadSegment road = 1;
  optional apollo.routing.Measurement measurement = 2;
}

message RoutingFeature {
  optional RoutingResponseFeature routing_response = 1;
  repeated string local_routing_lane_id = 2;  // local routing close to ADC
  optional RoutingResponseFeature local_routing = 3;
}

// based on apollo.perception.TrafficLight
message TrafficLightFeature {
  optional apollo.perception.TrafficLight.Color color = 1;
  optional string id = 2;
  optional double confidence = 3 [default = 1.0];
  optional double tracking_time = 4;
  optional bool blink = 5;
  optional double remaining_time = 6;
}

message TrafficLightDetectionFeature {
  optional double message_timestamp_sec = 1;
  repeated TrafficLightFeature traffic_light = 2;
}

message ADCTrajectoryPoint {
  optional double timestamp_sec = 1;  // localization measurement_time
  optional PlanningTag planning_tag = 2;
  optional CommonTrajectoryPointFeature trajectory_point = 3;
}

message LearningOutput {
  repeated TrajectoryPointFeature adc_future_trajectory_point = 1;
}

message LearningDataFrame {
  // localization message publishing time in seconds.
  optional double message_timestamp_sec = 1;
  optional uint32 frame_num = 2;
  optional string map_name = 3;

  optional PlanningTag planning_tag = 4;

  // Features from chassis
  optional ChassisFeature chassis = 5;

  // Features from localization
  optional LocalizationFeature localization = 6;

  // Features from perception obstacles
  repeated ObstacleFeature obstacle = 7;

  // Features from routing
  optional RoutingFeature routing = 8;

  // Features from traffic_light_detection
  optional TrafficLightDetectionFeature traffic_light_detection = 9;

  // ADC past trajectory
  repeated ADCTrajectoryPoint adc_trajectory_point = 10;

  // Learning ouputs
  optional LearningOutput output = 11;
}

message LearningData {
  repeated LearningDataFrame learning_data_frame = 1;
}

message PlanningLearningData {
  optional apollo.common.Header header = 1;
  optional LearningDataFrame learning_data_frame = 2;
}
